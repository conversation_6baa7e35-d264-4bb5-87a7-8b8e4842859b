@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.PageUI.RzDataGrid
@using eZeroCore.Web.Stk.Data
@using <PERSON><PERSON><PERSON>
@using System.Globalization
@using ZycCore.Models

@inject IJSRuntime JS
@inject IDialogService DialogService
@inject NavigationManager NavigationManager

@code {
    [Parameter] public BlzHelperObj? CObj { get; set; }
    [Parameter] public DateTime? DateStart { get; set; }
    [Parameter] public DateTime? DateEnd { get; set; }

    private List<StockOptionCallPutRatio.Data> Datas = new();
    private bool isLoading = false;
    private int PageSize = 20;
    private int TotalCount { get; set; }
    private int page { get; set; }
    private eZeroCore.Settings settings = new();

	protected override async Task OnInitializedAsync()
	{
		await LoadData();
	}

	protected override async Task OnParametersSetAsync()
	{
		// 当日期参数变化时重新加载数据
		await LoadData();
	}

	private async Task LoadData(LoadDataArgs? args = null)
	{
		isLoading = true;
		StateHasChanged();
		await Task.Delay(10);
		StockOptionCallPutRatio.List list = new()
		{

		};
		Datas = await list.GetListAsync(args, PageSize, DateStart, DateEnd,stockCode);
		TotalCount = list.TotalCount;
		page = list.SetPageNumber;
		isLoading = false;
	}

	public string stockCode { get; set; } = "";
	public async Task SearchByStockCode(string stock)
	{
		stockCode = stock.Trim();
		await Generic.Loading(JS);
		await LoadData();
		await Generic.Loading(JS, false);
	}

	private void NavigateToDashboard(string stockCode)
	{
		NavigationManager.NavigateTo($"Optiontrade?symbol={stockCode}", forceLoad: true);
	}

	string SearchTextValue { get; set; } = "";
	private RadzenTextBox searchTextBox;
	bool SearchMode { get; set; }
	[JSInvokable]
	public async Task HandleSearchKeyDown()
	{
		await Task.Delay(300);

		await LoadData();
		StateHasChanged();
	}
	async Task CancelSearchMode()
	{
		SearchTextValue = "";
		await HandleSearchKeyDown();
	}
	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
			if (searchTextBox is not null)
			{
				// 获取 RadzenTextBox 内部的输入元素并添加事件处理程序
				await JS.InvokeVoidAsync("eSc.inputAddEnterKeyHandler", searchTextBox.Element, DotNetObjectReference.Create(this), nameof(HandleSearchKeyDown));
			}
		}
	}

	DateRange DateRange
	{
		get => new(DateStart, DateEnd);
		set 
		{
			if (value != null)
			{
				DateStart = value.Start;
				DateEnd = value.End;
				_ = OnDateRangeChanged(); // 异步刷新数据
			}
		}
	}

	private async Task OnDateRangeChanged()
	{
		await LoadData();
		StateHasChanged();
	}

	// 添加行渲染样式处理方法
	private void OnRowRender(RowRenderEventArgs<StockOptionCallPutRatio.Data> args)
	{
		// 默认
		string className = "";

		// TODO: 这里的判断逻辑可能需要调整
		if (args.Data.Call_Premium > 10000000)
		{
			className = "rz-data-row flowOverviewBarRowFill";
		}
		else
		{
			className = "rz-data-row";
		}
		args.Attributes["class"] = className;
	}
}

<_UIFramework IncludeRowDiv="false" Class="mt-3" HeaderContentClass="d-flex justify-content-between">
	<HeaderContent>
		<div style="display: flex; align-items: center;color:rgba(245,156,26,1);font-weight:900;margin:6px">
			<span style="font-weight: 900; font-size: 14px;display: flex; align-items: center;color:rgba(245,156,26,1);">
				@(CObj?.UserLang.GetLangValue("Call Put 比例") ?? "Call Put 比例")
			</span>

			<MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
				<div style="margin-left: 8px;height: 20px; display: flex; align-items: center;background-color: rgba(245,156,26,1); color: #2d353c; padding: 4px 10px; border-radius: 8px; font-size: 12px; font-weight: bold;">
					@if (DateStart.HasValue && DateEnd.HasValue && DateStart.Value.Date != DateEnd.Value.Date)
					{
						@($"{DateStart.Value:yyyy-MM-dd} ~ {DateEnd.Value:yyyy-MM-dd}")
					}
					else
					{
						@(DateStart?.ToString("yyyy-MM-dd") ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().ToString("yyyy-MM-dd"))
					}
				</div>
			</MudHidden>
			
		</div>
	</HeaderContent>
	<ChildContent>
		<!-- PC端表格 - 正常显示 -->
		<MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
			<RzDataTable TItem="StockOptionCallPutRatio.Data"
						 Slim="true"
						 AllowPaging=true
						 AllowColumnResize="true"
						 Data="@Datas" PageSize="@PageSize"
						 IsLoading="@isLoading"
						 RowRenderCallback="@OnRowRender"
						 Count="@TotalCount" LoadData="@LoadData">

				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="60px" Sortable="true" Title="@(CObj?.UserLang.GetLangValue("更新日期") ?? "更新日期")" property="CreateDate" Frozen="true">
					<Template Context="data">
						<span>
							@data.CreateDate.ToString("MM/dd/yyyy HH:mm")
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@(CObj?.UserLang.GetLangValue("代号") ?? "代号")" property="Stock" Frozen="true">
					<Template Context="data">
						<span @onclick="() => NavigateToDashboard(data.Stock)" style="background-color:#212529; padding:1px 4px; border-radius:3px; font-weight:bold;cursor:pointer">
							@data.Stock
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@(CObj?.UserLang.GetLangValue("Call总权利金") ?? "Call总权利金")" Property="Call_Premium">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.CallPremium
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@(CObj?.UserLang.GetLangValue("Put总权利金") ?? "Put总权利金")" Property="Put_Premium">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.PutPremium
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@(CObj?.UserLang.GetLangValue("C/P总权利金比") ?? "C/P总权利金比")" Property="CallPutPremiumRatio">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.CallPutPremiumRatio?.ToString("F3")
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@CObj.UserLang.GetLangValue("看涨总权利金")" Property="TrendUpPremium">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.TrendUpPremium.ToString("$#,0")
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@CObj.UserLang.GetLangValue("看跌总权利金")" Property="TrendDownPremium">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.TrendDownPremium.ToString("$#,0")
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@CObj.UserLang.GetLangValue("看涨/看跌总权利金比")" property="TrendPremiumRatio">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.TrendPremiumRatio
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@CObj.UserLang.GetLangValue("Call总合约")" Property="Call_Size">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.Call_Size
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@CObj.UserLang.GetLangValue("Put总合约")" Property="Put_Size">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.Put_Size
						</span>
					</Template>
				</RadzenDataGridColumn>
				<RadzenDataGridColumn TItem="StockOptionCallPutRatio.Data" Width="45px" Sortable="true" Title="@CObj.UserLang.GetLangValue("C/P总合约数比")" Property="CallPutSizeRatio">
					<Template Context="data">
						<span style="display:block; text-align:right;max-width:85px">
							@data.CallPutSizeRatio?.ToString("0.####")
						</span>
					</Template>
				</RadzenDataGridColumn>
			</RzDataTable>
		</MudHidden>

		<!-- 移动端表格 - 使用组件化布局 -->
		<MudHidden Breakpoint="Breakpoint.MdAndUp">
			<div class="mobile-container">
				@if (isLoading)
				{
					<div style="display:flex; justify-content:center; margin:12px 0;">
						<MudProgressCircular Color="Color.Primary" Indeterminate="true" />
					</div>
				}
				else if (Datas?.Count > 0)
				{
					@foreach (var item in Datas)
					{
						var rowIndex = Datas.IndexOf(item);

						<div class="mobileDatatable" style="padding:1px 0; border-bottom:1px solid rgba(255,255,255,0.1); margin-bottom:3px;">
							<!-- 使用移动端表格头部组件 -->
							<_MobileTableHeader TimeValue="@item.CreateDate">
							</_MobileTableHeader>

							<!-- 使用左右布局组件 -->
							<_LeftRightLayout LeftWidth="13%">
								<LeftContent>
									<!-- 股票代号 -->
									<div style="text-align:center;">
										<a href="javascript:void(0)" @onclick="() => NavigateToDashboard(item.Stock)" style="text-decoration: none;">
											<span style="background-color:#212529; color:white; padding:1px 4px; border-radius:3px; font-size:17px; font-weight:bold;">
												@item.Stock
											</span>
										</a>
									</div>
								</LeftContent>
								<RightContent>
									<!-- 使用Grid布局替代两行flexbox -->
									<div style="display: grid; grid-template-columns: 1fr 1fr 1fr; grid-template-rows: auto auto auto; gap: 4px 12px; margin-left: 8px;">
										
										<!-- 第一行：Call总权利金、Put总权利金、C/P总权利金比 -->
										<div style="grid-column: 1; grid-row: 1;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("Call总权利金")</div>
											<div class="gd-value" style="color:#28a745;">@item.CallPremium</div>
										</div>

										<div style="grid-column: 2; grid-row: 1;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("Put总权利金")</div>
											<div class="gd-value" style="color:#dc3545;">@item.PutPremium</div>
										</div>

										<div style="grid-column: 3; grid-row: 1;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("C/P总权利金比")</div>
											<div class="gd-value">@(item.CallPutPremiumRatio?.ToString("F2") ?? "--")</div>
										</div>

										<!-- 第二行：看涨总权利金、看跌总权利金、看涨/看跌总权利金比 -->
										<div style="grid-column: 1; grid-row: 2;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("看涨总权利金")</div>
											<div class="gd-value" style="color:#28a745;">@item.TrendUpPremium.ToString("$#,0")</div>
										</div>

										<div style="grid-column: 2; grid-row: 2;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("看跌总权利金")</div>
											<div class="gd-value" style="color:#dc3545;">@item.TrendDownPremium.ToString("$#,0")</div>
										</div>

										<div style="grid-column: 3; grid-row: 2;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("看涨/看跌总权利金比")</div>
											<div class="gd-value">@item.TrendPremiumRatio</div>
										</div>

										<!-- 第三行：Call总合约、Put总合约、C/P总合约数比 -->
										<div style="grid-column: 1; grid-row: 3;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("Call总合约")</div>
											<div class="gd-value">@item.Call_Size</div>
										</div>

										<div style="grid-column: 2; grid-row: 3;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("Put总合约")</div>
											<div class="gd-value">@item.Put_Size</div>
										</div>

										<div style="grid-column: 3; grid-row: 3;">
											<div class="gd-label">@CObj.UserLang.GetLangValue("C/P总合约数比")</div>
											<div class="gd-value">@(item.CallPutSizeRatio?.ToString("F2") ?? "--")</div>
										</div>
									</div>
								</RightContent>
							</_LeftRightLayout>
						</div>
					}

					<div style="display:flex; justify-content:center; margin-top:16px;">
						<MudPagination Count="@((int)Math.Ceiling((double)TotalCount / PageSize))" 
									   SelectedChanged="async (int page) => { await LoadData(new LoadDataArgs { Skip = (page - 1) * PageSize, Top = PageSize }); }" 
									   Selected="page" />
					</div>
				}
				else
				{
					<div style="text-align:center; margin:16px 0; color:#6c757d;">
						<span>@CObj.UserLang.GetLangValue("暂无数据")</span>
					</div>
				}
			</div>
		</MudHidden>
	</ChildContent>
</_UIFramework>


