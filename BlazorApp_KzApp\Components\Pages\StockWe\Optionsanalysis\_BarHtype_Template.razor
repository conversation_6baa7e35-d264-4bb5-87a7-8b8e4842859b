﻿@inject IJSRuntime JS
@inject NavigationManager NavigationManager

@code {
    [Parameter] public BlzHelperObj? CObj { get; set; }
    [Parameter] public List<eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas> Datas { get; set; } = new();
    [Parameter] public bool Left { get; set; } = true;
    [Parameter] public bool isValue { get; set; } = false;
    [Parameter] public bool EnableNavigation { get; set; } = false;

    // 标题
    public string OneTitle { get; set; } = "";
    public string TwoTitle { get; set; } = "";

    // 状态
    private bool IsfirstRender { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            IsfirstRender = firstRender;
            StateHasChanged();
            await Task.Delay(0);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        if (CObj != null)
        {
            if (!isValue)
            {
                OneTitle = CObj.UserLang.GetLangValue("总权利金");
                TwoTitle = CObj.UserLang.GetLangValue("总合约");
            }
            else
            {
                OneTitle = CObj.UserLang.GetLangValue("总价值");
                TwoTitle = "";
            }
        }
        else
        {
            // 默认值
            if (!isValue)
            {
                OneTitle = "总权利金";
                TwoTitle = "总合约";
            }
            else
            {
                OneTitle = "总价值";
                TwoTitle = "";
            }
        }
    }
   
    private void NavigateToDashboard(string stockCode)
    {
        if (EnableNavigation)
        {
            NavigationManager.NavigateTo($"Optiontrade?symbol={stockCode}", forceLoad: true);
        }
    }
}

@if (IsfirstRender && Datas.Count > 0)
{
    <div class="@(Left? "bLeft" : "bRight")" style="background-color: #000; padding-bottom: 10px;padding-bottom: 20px;">

        <div class="BarHeading" style="color: white; text-align: center; font-size: 16px; padding: 5px 0; font-weight: bold;">
            @CObj.UserLang.GetLangValue(@Datas.FirstOrDefault()?.Title)
        </div>

        <div class="flowOverviewBar" style="display: flex; justify-content: @(Left ? "flex-end" : "flex-start"); padding-bottom: 8px; border-bottom: 1px solid #333; margin-bottom: 7px;">
            @if (!Left)
            {
                <div style="display: flex; ">
                    <div style="width: 80px; text-align: center; color: #ff5252; font-size: 13px; font-weight: bold;">
                        @CObj.UserLang.GetLangValue(OneTitle)
                    </div>
                    <div style="width: 80px; text-align: center; color: #ff5252; font-size: 13px; font-weight: bold;">
                        @CObj.UserLang.GetLangValue(TwoTitle)
                    </div>
                </div>
            }
            else
            {
                <div style="display: flex;">
                    <div style="width: 80px; text-align: center; color:rgb(4, 171, 74); font-size: 13px; font-weight: bold;">
                        @CObj.UserLang.GetLangValue(TwoTitle)
                    </div>
                    <div style="width: 80px; text-align: center; color:rgb(2, 155, 66); font-size: 13px; font-weight: bold;">
                        @CObj.UserLang.GetLangValue(OneTitle)
                    </div>
                </div>
            }
        </div>

        @foreach (eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas data in Datas)
        {
            <div class="barcontainer" style="margin: 0 0 6px 0; cursor: pointer; transition: all 0.3s ease; " 
                 onmouseover="this.style.opacity='0.9'; this.style.transform='translateX(@(Left ? "2" : "-2")px)';" 
                 onmouseout="this.style.opacity='1'; this.style.transform='translateX(0)';"
                 @onclick="() => NavigateToDashboard(data.MainLabel)">
                <div class="flowOverviewBar" style="border-radius: 8px;position: relative; display: flex; align-items: center; justify-content: @(Left ? "flex-end" : "flex-start"); height: 28px;">
                    <!-- 背景条 -->
                    <div class="flowOverviewBarRowFill" style="border-radius: 8px; width:@(data.WidthPerc)%; position: absolute; top: 0; @(Left ? "left" : "right"): 0; height: 100%; background: linear-gradient(to @(Left ? "right" : "left"), @(Left ? "rgba(46, 204, 113, 0.85)" : "rgba(231, 76, 60, 0.85)") 0%, @(Left ? "rgba(46, 204, 113, 0.1)" : "rgba(231, 76, 60, 0.1)") 100%); z-index: 1; border-radius: @(Left ? "0 8px 8px 0" : "8px 0 0 8px");"></div>
                    
                    @if (!Left)
                    {
                        <div style="display: flex; z-index: 2;">
                            <div style="width: 80px; text-align: center; color: #ff2d00; font-weight: bold; text-shadow: 0px 0px 1px rgba(0,0,0,0.2);">
                                @CObj.UserLang.GetLangValue(data.LableB)
                            </div>
                            <div style="width: 80px; text-align: center; color: #ff2d00; font-weight: bold; text-shadow: 0px 0px 1px rgba(0,0,0,0.2);">
                                @CObj.UserLang.GetLangValue(data.LableA)
                            </div>
                        </div>
                        <div style="flex: 1;"></div>
                        <div class="stockName" style="width: 60px; text-align: center; border-radius: 0 8px 8px 0; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 700; letter-spacing: 0.5px; transition: all 0.2s ease; text-shadow: 1px 1px 0 #000; position: relative; z-index: 1;">
                            <span style="color:#ffffff; position: relative; z-index: 2;border-radius: 8px">
                                @CObj.UserLang.GetLangValue(data.MainLabel)
                            </span>
                        </div>
                    }
                    else
                    {
                        <div class="stockName" style="width: 60px; text-align: center; border-radius: 8px 0 0 8px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 700; letter-spacing: 0.5px; transition: all 0.2s ease; text-shadow: 1px 1px 0 #000; position: relative; z-index: 1;">
                            <span style="color:#ffffff; position: relative; z-index: 2;">
                                @CObj.UserLang.GetLangValue(data.MainLabel)
                            </span>
                        </div>
                        <div style="flex: 1;"></div>
                        <div style="display: flex; z-index: 2;">
                            <div style="width: 80px; text-align: center; color: #00e676; font-weight: bold; text-shadow: 0px 0px 1px rgba(0,0,0,0.2);">
                                @CObj.UserLang.GetLangValue(data.LableA)
                            </div>
                            <div style="width: 80px; text-align: center; color: #00e676; font-weight: bold; text-shadow: 0px 0px 1px rgba(0,0,0,0.2);">
                                @CObj.UserLang.GetLangValue(data.LableB)
                            </div>
                        </div>
                    }
                </div>
            </div>
        }

    </div>
}