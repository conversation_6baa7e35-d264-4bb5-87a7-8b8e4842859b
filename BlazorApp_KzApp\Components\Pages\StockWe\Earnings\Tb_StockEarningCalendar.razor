@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.PageUI.RzDataGrid
@using eZeroCore.Web.Stk.Data
@using ZycCore.Models
@using ZycCore.Models.Dto
@using <PERSON><PERSON><PERSON>
@using System.Globalization
@using MudBlazor

@inject IJSRuntime JS

@code {

    [Parameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public DateTime? DateStart { get; set; }

    [Parameter]
    public DateTime? DateEnd { get; set; }

    [Parameter]
    public bool IsDarkData { get; set; }

    public string Title ;

    private List<StockEarningCalendar.Data> Datas = new();
    bool isLoading = false;
    int PageSize = 15;
    int TotalCount { get; set; }
    int page { get; set; }

    // 初始化逻辑
    protected override async Task OnInitializedAsync()
    {
        Title = CObj.UserLang.GetLangValue("股票财报日历");
        await Generic.Loading(JS);
        await LoadData();
        await Generic.Loading(JS, false);
    }

    // 监听参数变化
    protected override async Task OnParametersSetAsync()
    {
        // 当日期参数变化时重新加载数据
        await LoadData();
    }

    // 加载数据
    private async Task LoadData(LoadDataArgs? args = null)
    {
        isLoading = true;
        StateHasChanged();
        
        StockEarningCalendarDto dto = new()
        {
            DateStart = DateStart ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date,
            DateEnd = DateEnd ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date,
            StockCode = stockCode
        };
        
        StockEarningCalendar.List list = new();
        Datas = await list.DataTableListAsync(args, PageSize, dto);
        TotalCount = list.TotalCount;
        page = list.SetPageNumber;
        isLoading = false;
    }

    public string stockCode { get; set; } = "";
    // 添加接收股票代码的搜索方法
    public async Task SearchByStockCode(string stock)
    {
        stockCode = stock.Trim();
        await Generic.Loading(JS);
        await LoadData();
        await Generic.Loading(JS, false);
    }

    // 添加行渲染样式处理方法
    private void OnRowRender(RowRenderEventArgs<StockEarningCalendar.Data> args)
    {
        // 根据买入价值设置样式  
        // string className = args.Data.BuyValue_5 > 100000000 
        //     ? "rz-data-row flowOverviewBarRowFill" 
        //     : "rz-data-row";
        
        // args.Attributes["class"] = className;
    }
    
    // 格式化日期
    private string FormatDate(DateTime? date, string format = "yy/MM/dd")
    {
        return date?.ToString(format) ?? "--";
    }
    
    // 格式化数值
    private string FormatNumber(double? value, int decimals = 0)
    {
        if (value == null) return "--";
        return value.Value.ToString($"N{decimals}", CultureInfo.InvariantCulture);
    }
    
    // 格式化百分比
    private string FormatPercent(decimal? value, int decimals = 2)
    {
        if (value == null) return "--";
        return (value.Value * 100).ToString($"N{decimals}", CultureInfo.InvariantCulture) + "%";
    }

    // 格式化大数值显示 (万、亿) - double版本
    private string FormatLargeNumber(double? value)
    {
        if (value == null) return "--";
        var val = value.Value;
        if (val >= 100000000) // 1亿
        {
            return $"{val / 100000000:N1}亿";
        }
        else if (val >= 10000) // 1万
        {
            return $"{val / 10000:N1}万";
        }
        else
        {
            return $"{val:N0}";
        }
    }

    // 格式化期权数据显示
    private string FormatOptionData(string today, string past5, string past10)
    {
        var parts = new List<string>();
        if (!string.IsNullOrEmpty(today)) parts.Add($"{CObj.UserLang.GetLangValue("今")}:{today}");
        if (!string.IsNullOrEmpty(past5)) parts.Add($"{CObj.UserLang.GetLangValue("5天")}:{past5}");
        if (!string.IsNullOrEmpty(past10)) parts.Add($"{CObj.UserLang.GetLangValue("10天")}:{past10}");
        return parts.Count > 0 ? string.Join(" | ", parts) : "--";
    }
}

<_UIFramework IncludeRowDiv="false" Class="mt-3" HeaderContentClass="d-flex justify-content-between py-1">
    <HeaderContent>
        <div style="margin-top: 10px;display: flex; align-items: center;color:rgba(245,156,26,1);font-weight:900;margin:6px;">
            <span style="font-weight: 900; font-size: 14px;display: flex; align-items: center;color:rgba(245,156,26,1);">
                <RadzenIcon Icon="event_note" Style="margin-right: 4px;" />
                @Title
            </span>
            <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
                <div style="margin-left: 8px;height: 20px; display: flex; align-items: center;background-color: rgba(245,156,26,1); color: #2d353c; padding: 4px 10px; border-radius: 8px; font-size: 14px; font-weight: bold;">
                    @if (DateStart.HasValue && DateEnd.HasValue && DateStart.Value.Date != DateEnd.Value.Date)
                    {
                        @($"{DateStart.Value:yyyy-MM-dd} ~ {DateEnd.Value:yyyy-MM-dd}")
                    }
                    else
                    {
                        @(DateStart?.ToString("yyyy-MM-dd") ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().ToString("yyyy-MM-dd"))
                    }
                </div>
            </MudHidden>
        </div>
    </HeaderContent>
    <ChildContent> 
        <!-- PC端表格 - 正常显示 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
            <RzDataTable TItem="StockEarningCalendar.Data"
                         Slim="true"
                         AllowPaging=true
                         AllowColumnResize="true"
                         Data="@Datas" PageSize="@PageSize"
                         IsLoading="@isLoading"
                         RowRenderCallback="@OnRowRender"
                         Count="@TotalCount" LoadData="@LoadData">

                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="CreateDate" Sortable="true" Width="130px" Title="@CObj.UserLang.GetLangValue("更新日期")" Frozen="true">
                    <Template Context="data">
                        <span>
                            @data.CreateDate?.ToString("MM/dd HH:mm")
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="Stock" Width="75px" Sortable="true" Title="@CObj.UserLang.GetLangValue("代号")" Frozen="true">
                    <Template Context="data">
                        <span>
                            @data.Stock
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="EarningDate" Width="150px" Sortable="false" Title="@CObj.UserLang.GetLangValue("财报日期")">
                    <Template Context="data">
                        <span>
                            @FormatDate(data.EarningDate, "yy-MM-dd")
                            (@data.WeekDay)
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data"  property="OpenClose" Width="80px" Sortable="false" Title="@CObj.UserLang.GetLangValue("盘前盘后")">
                    <Template Context="data">
                        <span class="badge" style="@(data.OpenClose?.Contains("前") == true ? "background-color: #1f5688;" : "background-color: #513285;") border-radius: 4px; padding: 4px 8px;">
                            @data.OpenClose
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="Call_VS_Put_0" Width="280px" Sortable="true" Title="@CObj.UserLang.GetLangValue("期权总权利金 看涨 VS 看跌")">
                    <Template Context="data">
                        <div>
                            <span class="d-block">
                                @CObj.UserLang.GetLangValue("今天"): @(data.Call_VS_Put_0 ?? "--")
                            </span>
                            <span class="d-block">
                                @CObj.UserLang.GetLangValue("过去五天"): @(data.Call_VS_Put_5 ?? "--")
                            </span>
                            <span class="d-block">
                                @CObj.UserLang.GetLangValue("过去十天"): @(data.Call_VS_Put_10 ?? "--")
                            </span>
                        </div>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="BuyValue_5" Width="220px" Sortable="true" Title="@CObj.UserLang.GetLangValue("过去5天暗池+大单买入总价值")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;" class="text-green f-w-700">
                            @FormatNumber(data.BuyValue_5)
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="SellValue_5" Width="220px" Sortable="true" Title="@CObj.UserLang.GetLangValue("过去5天暗池+大单卖出总价值")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;" class="text-red">
                            @FormatNumber(data.SellValue_5)
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="BuySellValueRatio_5" Width="220px" Sortable="true" Title="@CObj.UserLang.GetLangValue("过去5天暗池+大单买卖价值比")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            @FormatPercent(data.BuySellValueRatio_5)
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="CompanyName" Width="200px" Sortable="true" Title="@CObj.UserLang.GetLangValue("公司名")">
                    <Template Context="data">
                        <span>
                            @data.CompanyName
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="Actual" Width="90px" Sortable="true" Title="@CObj.UserLang.GetLangValue("实际EPS")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            @FormatNumber(data.Actual, 2)
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="SPconsensus" Width="90px" Sortable="true" Title="@CObj.UserLang.GetLangValue("预期")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            @FormatNumber(data.SPconsensus, 2)
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="YrAgo" Width="90px" Sortable="true" Title="@CObj.UserLang.GetLangValue("一年前")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            @FormatNumber(data.YrAgo, 2)
                        </span>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="StockEarningCalendar.Data" property="YoYRevPercent" Width="90px" Sortable="true" Title="@CObj.UserLang.GetLangValue("同比")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            @data.YoYRevPercent
                        </span>
                    </Template>
                </RadzenDataGridColumn>
            </RzDataTable>
        </MudHidden>

        <!-- 移动端表格 - 使用组件化布局 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <div class="mobile-container">
                @if (isLoading)
                {
                    <div style="display:flex; justify-content:center; margin:12px 0;">
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </div>
                }
                else if (Datas?.Count > 0)
                {
                    @foreach (var item in Datas)
                    {
                        var rowIndex = Datas.IndexOf(item);

                        <div class="mobileDatatable" style="padding:1px 0; border-bottom:1px solid rgba(255,255,255,0.1); margin-bottom:3px;">
                            <!-- 使用移动端表格头部组件 -->
                            <_MobileTableHeader TimeValue="@item.CreateDate">
                                <CustomContent>
                                    <div style="font-size:12px; font-weight:500; color:white; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 180px;">
                                        @item.CompanyName
                                    </div>
                                </CustomContent>
                            </_MobileTableHeader>

                            <!-- 使用左右布局组件 -->
                            <_LeftRightLayout LeftWidth="17%">
                                <LeftContent>
                                    <!-- 股票代号区域 -->
                                    <div style="display:flex; flex-direction:column; gap:4px; align-items:center;">
                                        <!-- 股票代号 -->
                                        <div style="text-align:center;">
                                            <span style="background-color:#212529; color:white; padding:1px 4px; border-radius:3px; font-size:17px; font-weight:bold;">
                                                @item.Stock
                                            </span>
                                        </div>
                                        <!-- 盘前盘后 -->
                                        <div style="text-align:center;">
                                            <span class="badge" style="@(item.OpenClose?.Contains("前") == true ? "background-color: #1f5688;" : "background-color: #513285;") border-radius: 4px; padding: 2px 6px; font-size:10px; color:white;">
                                                @item.OpenClose
                                            </span>
                                        </div>
                                    </div>
                                </LeftContent>
                                <RightContent>
                                    <!-- 使用Grid布局 2行4列 -->
                                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; grid-template-rows: auto auto; gap: 2px 2px; margin-left: 8px;">
                                        
                                        <!-- 第一行：财报日期、买入价值、卖出价值、买卖价值比 -->
                                        <div style="grid-column: 1; grid-row: 1;">
                                            <div class="gd-label">@CObj.UserLang.GetLangValue("财报日期")</div>
                                            <div class="gd-value" style="color:#fbbf24;">
                                                @FormatDate(item.EarningDate, "MM/dd")
                                                <span style="font-size:9px; margin-left:2px;">(@item.WeekDay)</span>
                                            </div>
                                        </div>

                                        <div style="grid-column: 2; grid-row: 1;">
                                            <div class="gd-label" >@CObj.UserLang.GetLangValue("过去5天暗池+大单买入总价值")</div>
                                            <div class="gd-value" style="color:#28a745;">@FormatLargeNumber(item.BuyValue_5)</div>
                                        </div>

                                        <div style="grid-column: 3; grid-row: 1;">
                                            <div class="gd-label">@CObj.UserLang.GetLangValue("过去5天暗池+大单卖出总价值")</div>
                                            <div class="gd-value" style="color:#dc3545;">@FormatLargeNumber(item.SellValue_5)</div>
                                        </div>

                                        <div style="grid-column: 4; grid-row: 1;">
                                            <div class="gd-label">@CObj.UserLang.GetLangValue("过去5天暗池+大单买卖价值比")</div>
                                            <div class="gd-value">@FormatPercent(item.BuySellValueRatio_5)</div>
                                        </div>

                                        <!-- 第二行：实际EPS、预期EPS、一年前EPS、同比 -->
                                        <div style="grid-column: 1; grid-row: 2;">
                                            <div class="gd-label">@CObj.UserLang.GetLangValue("实际EPS")</div>
                                            <div class="gd-value">@FormatNumber(item.Actual, 2)</div>
                                        </div>

                                        <div style="grid-column: 2; grid-row: 2;">
                                            <div class="gd-label">@CObj.UserLang.GetLangValue("预期EPS")</div>
                                            <div class="gd-value">@FormatNumber(item.SPconsensus, 2)</div>
                                        </div>

                                        <div style="grid-column: 3; grid-row: 2;">
                                            <div class="gd-label">@CObj.UserLang.GetLangValue("一年前EPS")</div>
                                            <div class="gd-value">@FormatNumber(item.YrAgo, 2)</div>
                                        </div>

                                        <div style="grid-column: 4; grid-row: 2;">
                                            <div class="gd-label">@CObj.UserLang.GetLangValue("同比")</div>
                                            <div class="gd-value">
                                                @{
                                                    string yoyText = item.YoYRevPercent ?? "--";
                                                    string yoyColor = "#6c757d";
                                                    
                                                    if (decimal.TryParse(item.YoYRevPercent?.Replace("%", ""), out decimal yoyValue))
                                                    {
                                                        yoyColor = yoyValue > 0 ? "#28a745" : "#dc3545";
                                                    }
                                                }
                                                <span style="color:@yoyColor">@yoyText</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 期权数据独立于Grid外 -->
                                    <div style="margin-left:8px; white-space: nowrap;height:22px">
                                        <span style=" font-size:10px; line-height:1; margin-bottom:1px;">@CObj.UserLang.GetLangValue("期权今天")</span>
                                        <span style="font-size:11px; font-weight:600; height: 16px; margin-left:8px ">
                                            @(item.Call_VS_Put_0 ?? "--")
                                        </span>
                                    </div>

                                    <div style="margin-left:8px;white-space: nowrap;height:22px">
                                        <span style=" font-size:10px; line-height:1; margin-bottom:1px;">@CObj.UserLang.GetLangValue("期权5天")</span>
                                        <span style="font-size:11px; font-weight:600; height: 16px; margin-left:8px">
                                            @(item.Call_VS_Put_5 ?? "--")
                                        </span>
                                    </div>

                                    <div style="margin-left:8px;white-space: nowrap;height:22px">
                                        <span style="font-size:10px; line-height:1; margin-bottom:1px;">@CObj.UserLang.GetLangValue("期权10天")</span>
                                        <span style="font-size:11px; font-weight:600; height: 16px;margin-left:8px ">
                                            @(item.Call_VS_Put_10 ?? "--")
                                        </span>
                                    </div>
                                </RightContent>
                            </_LeftRightLayout>
                        </div>
                    }

                    <div style="display:flex; justify-content:center; margin-top:16px;">
                        <MudPagination Count="@((int)Math.Ceiling((double)TotalCount / PageSize))" 
                                       SelectedChanged="async (int page) => { await LoadData(new LoadDataArgs { Skip = (page - 1) * PageSize, Top = PageSize }); }" 
                                       Selected="page" />
                    </div>
                }
                else
                {
                    <div style="text-align:center; margin:16px 0; color:#6c757d;">
                        <span>@CObj.UserLang.GetLangValue("暂无数据")</span>
                    </div>
                }
            </div>
        </MudHidden>
    </ChildContent>
</_UIFramework>


