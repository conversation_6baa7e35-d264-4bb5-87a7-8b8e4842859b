﻿@inject IJSRuntime JS
@code {
    [Parameter] public RenderFragment? TitleContent { get; set; }
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public RenderFragment? HtypeBar { get; set; }

    [Parameter]
    public string HtypeBarClass { get; set; } = "barZeChart";

    [Parameter]
    public string ID { get; set; } = eZeroCore.EString.CreateId("DataBar");

    [Parameter]
    public string ClassName { get; set; } = "";

    [Parameter]
    public BlzHelperObj CObj { get; set; }

    [Parameter]
    public string Style { get; set; } = "";


    protected override bool ShouldRender()
    {
        return true;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Task.Delay(0);
        }
    }
}


<div class="card p-0 stockChart mt-2 @ClassName">
    <div class="card-header @CObj.IsMobile @(CObj.IsMobile?"p-1 px-x text-center":"p-2 px-3 d-flex justify-content-between align-items-center")" style="@Style">
        <div class="h5 mb-0 stockChartTitle">
            @if (TitleContent is not null)
            {
                @TitleContent
            }
        </div>
        @if (ChildContent is not null)
        {
            <div>
                <div class="btn-group @(CObj.IsMobile?"Scale80 w-100 my-2":"")" role="group">
                    @ChildContent
                </div>
            </div>
        }
    </div>
    @if (HtypeBar is null)
    {
        //calc(100vh - 180px)
        <div class="card-body px-0" id="@ID" data-delay="true" style="height:@(CObj.IsMobile?"300px":"400px");"></div>
    }
    else
    {
        <div class="card p-0 d-block custom-scrollbar-x">
            <div id="@ID" class="@HtypeBarClass">
                @HtypeBar
            </div>
        </div>
    }
</div>