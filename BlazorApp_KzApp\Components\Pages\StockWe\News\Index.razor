@using ZycCore.Models
@using ZycCore
@using ZycCore.Models.Dto
@using <PERSON><PERSON><PERSON>
@using MudBlazor
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.PageUI.RzDataGrid
@inject NavigationManager NavigationManager
@inject IJSRuntime JS
@* @inject EventSearchService EventService *@
@implements IDisposable

@page "/News/{param?}"

@code {
    [Parameter]
    public string param { get; set; } = "";

    string SearchString = "";
    string SearchTitle = "";

    void OnEnterPressed(string inputValue)
    {
        SearchString = inputValue?.ToUpper() ?? "";
        if (!string.IsNullOrEmpty(SearchString))
        {
            InvokeAsync(() =>
            {
                if (SearchString.Length > 0)
                {
                    StateHasChanged();
                    _ = SearchNews();
                    _ = JS.InvokeVoidAsync($"eSc.toolTipFuncOnClickHide", SearchCloseButtonId);
                }
                else
                {
                    _ = JS.InvokeVoidAsync($"alert", CObj.UserLang.GetLangValue("搜索需要至少1个字符."));
                }
            });
        }
    }

    async Task OnSearch(string value)
    {
        SearchTitle = value?.Trim() ?? "";
        if (string.IsNullOrEmpty(SearchTitle))
        {
            await ClearAndReload();
            return;
        }

        if (SearchTitle.Length > 0)
        {
            await Generic.Loading(JS);
            PageIndex = 0;
            newsItems.Clear();
            totalCount = 0;
            await LoadNews();
            await Generic.Loading(JS, false);
            await SafeJsInvokeAsync("Stock.scrollNewsToTop");
            StateHasChanged();
        }
        else
        {
            await JS.InvokeVoidAsync($"alert", CObj.UserLang.GetLangValue("搜索需要至少1个字符."));
        }
    }

    async Task ClearAndReload()
    {
        SearchTitle = "";
        SearchString = "";
        PageIndex = 0;
        newsItems.Clear();
        totalCount = 0;
        await LoadNews();
        StateHasChanged();
    }

    public void Dispose()
    {
        // EventService.OnEnterSearchPressed -= OnEnterPressed;
        _timer?.Dispose();
    }

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    eZeroCore.Users.User User { get; set; } = new();
    string SearchCloseButtonId = "btnNewsSearchCloseButton";
    private Timer _timer;

    // 新闻列表相关变量
    private List<StockNews.Data> newsItems = new();
    private string sCacheName = "";
    int tmerMin = 10;
    int ListPageSize = 30;
    int PageIndex = 0;
    StockNews.List newsList = new();
    int totalCount = 0;

    RzSearchInputBox inputBoxPC;     // PC端搜索框引用
    RzSearchInputBox inputBoxMobile; // 移动端搜索框引用

    protected override async Task OnInitializedAsync()
    {
        // EventService.OnEnterSearchPressed += OnEnterPressed;
        User = new(CObj.UserId);
        await LoadNews();
        _timer = new Timer(async state => await RefreshNews(), null, TimeSpan.FromMinutes(tmerMin), TimeSpan.FromMinutes(tmerMin));
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                if (!CObj.IsMobile)
                {
                    bool overlayScrollExists = await JS.InvokeAsync<bool>("eval", "typeof OverlayScrollbars !== 'undefined'");
                    if (overlayScrollExists)
                    {
                        await JS.InvokeVoidAsync($"Stock.initOverlayScroll");
                    }
                }

                bool stopCopyExists = await JS.InvokeAsync<bool>("eval", "typeof eSc !== 'undefined' && typeof eSc.stopCopy === 'function'");
                if (stopCopyExists)
                {
                    await JS.InvokeVoidAsync($"eSc.stopCopy", "美股大数据 StockWe.com");
                }
            }
            catch
            {
                // 忽略JavaScript调用错误
            }
        }
    }

    private async Task SafeJsInvokeAsync(string functionName, params object[] args)
    {
        try
        {
            string checkScript = $"typeof {functionName.Split('.')[0]} !== 'undefined'";
            if (functionName.Contains("."))
            {
                string[] parts = functionName.Split('.');
                checkScript = $"typeof {parts[0]} !== 'undefined' && typeof {functionName} === 'function'";
            }

            bool functionExists = await JS.InvokeAsync<bool>("eval", checkScript);
            if (functionExists)
            {
                await JS.InvokeVoidAsync(functionName, args);
            }
        }
        catch
        {
            // 忽略JavaScript调用错误
        }
    }

    int SelectedTypeIndex = 0;
    async Task OnSelectTab(int index)
    {
        try
        {
            await Generic.Loading(JS);
            SearchString = "";
            SearchTitle = "";
            SelectedTypeIndex = index;
            PageIndex = 0;
            newsItems.Clear();
            totalCount = 0;
            await LoadNews();
            StateHasChanged();
            await Generic.Loading(JS, false);
            await SafeJsInvokeAsync("eval", "if(typeof Stock !== 'undefined' && typeof Stock.scrollNewsToTop === 'function') { Stock.scrollNewsToTop(); }");
        }
        catch
        {
            await Generic.Loading(JS, false);
        }
    }

    async Task SearchNews()
    {
        await Generic.Loading(JS);
        PageIndex = 0;
        newsItems.Clear();
        totalCount = 0;
        await LoadNews();
        await Generic.Loading(JS, false);
        await SafeJsInvokeAsync("Stock.scrollNewsToTop");
        StateHasChanged();
    }

    string GetSqlWhere(int index)
    {
        return index switch
        {
            0 => "and SummaryChinese <> ''",
            1 => "and SummaryChinese <> '' and newstype = 'NewFil' and (Stock IS NOT NULL and stock <> '')",
            2 => "and SummaryChinese <> '' and newstype = 'ChinaNews'",
            3 => "and SummaryChinese <> '' and newstype = 'NewFil' and (Stock IS NULL or Stock = '')",
            _ => "and SummaryChinese <> ''"
        };
    }

    bool autoRefreshNews = true;
    async Task RefreshNews()
    {
        if (!autoRefreshNews) return;

        if (eZeroCore.AppSettings.IsTestModeAll)
        {
            await InvokeAsync(() =>
            {
                try
                {
                    NavigationManager.NavigateTo($"/news?tm={DateTime.Now.Minute.ToString()}");
                }
                catch
                {
                    // 忽略导航错误
                }
            });
        }

        await Generic.Loading(JS);
        PageIndex = 0;
        newsItems.Clear();
        totalCount = 0;
        await LoadNews();
        await Generic.Loading(JS, false);
        await InvokeAsync(() =>
        {
            StateHasChanged();
            SafeJsInvokeAsync("Stock.scrollNewsToTop").GetAwaiter().GetResult();
        });
    }

    async Task LoadNews()
    {
        PageIndex++;

        StockNewsDto dto = new()
        {
            DateStart = DateTime.UtcNow.AddDays(-30),
            DateEnd = DateTime.UtcNow,
            StockCode = !string.IsNullOrWhiteSpace(SearchString) ? SearchString.Trim() : "",
            SearchTitle = !string.IsNullOrWhiteSpace(SearchTitle) ? SearchTitle.Trim() : "",
            AddWhere = GetSqlWhere(SelectedTypeIndex)
        };

        try
        {
            newsList = new StockNews.List();
            var loadDataArgs = new LoadDataArgs
            {
                Skip = (PageIndex - 1) * ListPageSize,
                Top = ListPageSize
            };

            var result = await newsList.DataTableListAsync(loadDataArgs, ListPageSize, dto);
            
            if (PageIndex == 1)
            {
                totalCount = newsList.TotalCount;
            }
            
            newsItems.AddRange(result);
            sCacheName = newsList.CacheName;
        }
        catch
        {
            // 静默处理错误
        }
    }

    string GetClassName(StockNews.Data news)
    {
        if (SelectedTypeIndex == 0) return "warning";

        if (news.NewsType?.Equals("newfil", StringComparison.OrdinalIgnoreCase) == true)
        {
            return string.IsNullOrEmpty(news.Stock) ? "primary" : "success";
        }

        if (news.NewsType?.Equals("chinanews", StringComparison.OrdinalIgnoreCase) == true)
        {
            return "danger";
        }

        return "warning";
    }

    void ClearSearch()
    {
        SearchString = "";
        _ = SearchNews();
    }

    static string SearchColorView(string sContent, string SearchString)
    {
        if (!string.IsNullOrEmpty(SearchString) && !string.IsNullOrEmpty(sContent))
        {
            return sContent.Replace(SearchString, "<span class=\"fw-bold text-warning\">" + SearchString + "</span>");
        }
        return sContent ?? "";
    }

    string GetSummaryString(string newsId, string Summary)
    {
        if (!eZeroCore.EString.CkIsChinese(Summary))
        {
            _ = CheckTrans(newsId, Summary);
            return "";
        }
        return Summary;
    }

    async Task CheckTrans(string newsId, string Summary)
    {
        if (!eZeroCore.EString.CkIsChinese(Summary))
        {
            eZeroCore.Web.StkBlz.StockNews.NewsItem news = new(newsId);
            news.SummaryChinese = await EString.TranslateAsync(Summary);
            await news.UpdateData();
            eZeroCore.Db.CacheHelperV2.RemoveCache(sCacheName);
        }
    }

    async Task SearchButtonClick()
    {
        // 根据当前设备类型调用对应的搜索框
        if (!CObj.IsMobile && inputBoxPC != null)
        {
            await inputBoxPC.HandleSearchKeyDown();
        }
        else if (CObj.IsMobile && inputBoxMobile != null)
        {
            await inputBoxMobile.HandleSearchKeyDown();
        }
    }
}
<Main_Framework>

    <div class="row g-2 mt-1 newsTimelingPanel" style="height: 100vh;">

        <div class="col-lg-6 mt-0  NewsScrollbar vstack h-100" style="overflow-y:scroll">

            <div class="card card-body p-2 pb-0 ps-0 newsTimelingToolsBar" style="max-height:140px">
                <!-- Share feed toolbar END -->
                <ul class="nav nav-tabs nav-bottom-line tab-list"
                    role="tablist">
                    <li class="ps-1 pt-1 onlyPC" style="font-size:.8rem;display:flex;align-items:center;align-content:center;color:#a1a1a8">
                        @CObj.UserLang.GetLangValue("以下为")：@eZeroCore.DateTimeConvert.GetTimeZoneName(User.TimeZoneEnum)@CObj.UserLang.GetLangValue("时间")
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link @(SelectedTypeIndex == 0 ? "active" : "")"
                           style="color: @(SelectedTypeIndex == 0 ? "#0f6fec" : "#a1a1a8"); transition: color 0.3s ease; @(SelectedTypeIndex == 0 ? "border-color: #0f6fec;" : "")"
                           @onclick="async () => await OnSelectTab(0)"
                           href="javascript:void(0);"
                           aria-selected="@(SelectedTypeIndex == 0)" role="tab">
                            @CObj.UserLang.GetLangValue("全部新闻")
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link @(SelectedTypeIndex == 1 ? "active" : "")"
                           style="color: @(SelectedTypeIndex == 1 ? "#0f6fec" : "#a1a1a8"); transition: color 0.3s ease; @(SelectedTypeIndex == 1 ? "border-color: #0f6fec;" : "")"
                           @onclick="async () => await OnSelectTab(1)"
                           href="javascript:void(0);"
                           aria-selected="@(SelectedTypeIndex == 1)" role="tab">
                            @CObj.UserLang.GetLangValue("美股新闻")
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link @(SelectedTypeIndex == 2 ? "active" : "")"
                           style="color: @(SelectedTypeIndex == 2 ? "#0f6fec" : "#a1a1a8"); transition: color 0.3s ease; @(SelectedTypeIndex == 2 ? "border-color: #0f6fec;" : "")"
                           @onclick="async () => await OnSelectTab(2)"
                           href="javascript:void(0);"
                           aria-selected="@(SelectedTypeIndex == 2)" role="tab">
                            @CObj.UserLang.GetLangValue("中国新闻")
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link @(SelectedTypeIndex == 3 ? "active" : "")"
                           style="color: @(SelectedTypeIndex == 3 ? "#0f6fec" : "#a1a1a8"); transition: color 0.3s ease; @(SelectedTypeIndex == 3 ? "border-color: #0f6fec;" : "")"
                           @onclick="async () => await OnSelectTab(3)"
                           href="javascript:void(0);"
                           aria-selected="@(SelectedTypeIndex == 3)" role="tab">
                            @CObj.UserLang.GetLangValue("国际新闻")
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0 ps-3">
                            <h6 class="mb-0 opacity-75 me-2">
                                <i class="fa-solid fa-wand-magic-sparkles text-primary"></i>
                                @CObj.UserLang.GetLangValue("自动更新数据")
                            </h6>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" @bind=@autoRefreshNews>
                            </div>
                        </div>
                    </li>
                </ul>

                <!-- PC端搜索框 -->
                <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
                    <div class="p-2" style="z-index: 5; display: flex; align-items: center; gap: 8px; width: 100%;">
                        <div class="news-pc-search-box" style="flex: 1; height:28px;">
                            <RzSearchInputBox @ref=inputBoxPC OnSearch="@OnSearch" Placeholder="@CObj.UserLang.GetLangValue("搜索股票代号或关键词")"
                                              Style="width: 100%; height: 100%;" />
                        </div>
                        <_SearchButton Text="@CObj.UserLang.GetLangValue("搜索")"
                                       OnClick="@SearchButtonClick"
                                       MarginLeft="0"
                                       MarginTop="0"
                                       Height="28px"
                                       CustomStyle="margin-top:2px;white-space: nowrap; display: flex; align-items: center; justify-content: center; min-width: fit-content;" />
                    </div>
                </MudHidden>

                <MudHidden Breakpoint="Breakpoint.MdAndUp">
                    <div class="p-2 UIFrameworkHeader position-relative z-5 z-center mt-1" style="display:flex; align-items:center; gap:8px;">
                        <div>
                            <RzSearchInputBox @ref=inputBoxMobile OnSearch="@OnSearch" Placeholder="@CObj.UserLang.GetLangValue("搜索新闻")" />
                        </div>
                        <_SearchButton Text="@CObj.UserLang.GetLangValue("搜索")"
                                       OnClick="@SearchButtonClick"
                                       MarginLeft="0"
                                       MarginTop="0" />
                    </div>
                </MudHidden>
            </div>

            @if (!string.IsNullOrEmpty(SearchString))
            {
                <small class="d-block px-2 py-1 alert alert-warning alert-dismissible fade show NewsSearchInfoBar">
                    @CObj.UserLang.GetLangValue("检索所有")"@SearchString"@CObj.UserLang.GetLangValue("的相关内容")：
                    @if (newsItems.Count == 0)
                    {
                        <span class="text-danger ms-2">
                            <br />
                            @CObj.UserLang.GetLangValue("没有检索到相关内容，请尝试其他关键词.")
                        </span>
                    }
                    <button type="button" id="@SearchCloseButtonId"
                            data-bs-placement="top"
                            data-bs-custom-class="custom-tooltip"
                            data-bs-title="@CObj.UserLang.GetLangValue("退出搜索，返回新闻列表")"
                            data-trigger="hover focus"
                            @onclick=ClearSearch
                            class="btn-close p-1 pt-2 pe-2"></button>
                </small>
            }

            <div class="timeline" style="position: relative;">
                @if (newsItems.Count > 0)
                {
                    bool isFirstItem = true;
                    bool isLastItem = false;
                    int index = 0;

                    foreach (var news in newsItems)
                    {
                        // 检查是否为最后一个条目
                        index++;
                        isLastItem = (index == newsItems.Count);

                        // User.TimeZoneEnum
                        string sSummary = GetSummaryString(news.ID ?? "", news.SummaryChinese ?? "");
                        (string s, bool isNew, DateTime lctm) = eZeroCore.DateTimeConvert.GetDateDisplay(news.CreateDate, User.TimeZoneEnum);
                        if (!string.IsNullOrEmpty(sSummary))
                        {
                            <div class="tl-entry" style="position: relative; padding-left: 40px; margin-bottom: 15px;">

                                <!-- 第一个条目的上方添加一个遮罩，遮住蓝线 -->
                                @if (isFirstItem)
                                {
                                    <div style="position: absolute; left: 20px; top: -15px; height: 40px; width: 2px; background-color: #121010; z-index: 3;"></div>
                                }

                                <!-- 图标div，添加背景色和边距，确保能遮挡蓝线 -->
                                <div style="position: absolute; left: 20px; top: 15px; transform: translateX(-50%); background-color: #121010; padding: 8px 0; z-index: 5;">
                                    <div style="width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center;" class="bg-@(GetClassName(news))">
                                        <i class="fa-regular fa-file-lines text-info-subtle"></i>
                                    </div>
                                </div>

                                <!-- 连接线段（不是最后一项时显示） -->
                                @if (!isLastItem)
                                {
                                    <div style="position: absolute; left: 20px; top: 50px; width: 2px; bottom: -15px; background-color: #6ea8fe; z-index: 1;"></div>
                                }

                                <!-- 新闻卡片 -->
                                <div class="card" style="margin-left: 10px; background-color: var(--dark-Card, #121010); border: 1px solid rgba(200, 203, 189, 0.4); border-radius: 8px; margin-top: 7px;">
                                    <div class="card-body p-2">
                                        <div style="color: #6ea8fe; font-size: 0.8rem; margin-bottom: 3px;">
                                            @lctm.ToString("yyyy-MM-dd HH:mm") (@CObj.UserLang.GetLangValue("美国东岸"))

                                            @* 添加股票代号显示 *@
                                            @if (!string.IsNullOrEmpty(news.Stock))
                                            {
                                                <span class="badge bg-primary fw-bold ms-2 text-info-subtle" style="transform: scale(0.85);">
                                                    @(new MarkupString(news.Stock.Replace(":", "<br/>")))
                                                </span>
                                            }
                                        </div>
                                        <div class="copyBlock h6 card-title" style="font-size: 0.89rem;">
                                            @(new MarkupString(SearchColorView(news.TitleChinese ?? "", SearchString)))
                                        </div>
                                        <blockquote class="blockquote">
                                            <p class="copyBlock" style="font-size: 0.85rem;">
                                                @(new MarkupString(SearchColorView(sSummary, SearchString)))
                                            </p>
                                        </blockquote>
                                    </div>
                                </div>
                            </div>
                            // 将isFirstItem设置为false，以便后续条目都显示上半部分蓝线
                            isFirstItem = false;
                        }
                    }
                }
            </div>

            @if (newsItems.Count >= ListPageSize && newsItems.Count < totalCount)
            {
                <div class="d-grid gap-2 col-6 mx-auto mt-3 mb-3 loadNewsBttonBar">
                    <button class="btn btn-outline-info" @onclick="() => LoadNews()" type="button">
                        <i class="fa-solid fa-square-plus me-2"></i> @CObj.UserLang.GetLangValue("加载更多")@(string.IsNullOrEmpty(SearchString) ? "" : CObj.UserLang.GetLangValue("有关") + "\"" + SearchString + "\"" + CObj.UserLang.GetLangValue("的"))@CObj.UserLang.GetLangValue("新闻")
                        @if (newsItems.Count > 0 && totalCount > 0)
                        {
                            <span class="ms-1">(@newsItems.Count/@totalCount)</span>
                        }
                    </button>
                </div>
            }
            else if (newsItems.Count > 0 && totalCount > 0 && newsItems.Count >= totalCount)
            {
                <div class="text-center mt-2 mb-1">
                    <small class="text-muted">@CObj.UserLang.GetLangValue("已加载全部") @totalCount @CObj.UserLang.GetLangValue("条新闻")</small>
                </div>
            }

            <div class="loadNewsBttonBarBottomBlock">&nbsp;</div>

        </div>

        <div class="col-lg-5 mt-0 navbar-expand-lg h-100" style=" height: 95% !important; display: flex; flex-direction: column;">
            <iframe src="/iframe/chat/DefalultPublicStkUsersNewsChatGroup" class="ContentIframe border border-0 w-100" style="flex: 1 1 auto; min-height: 500px;"></iframe>
        </div>
    </div>


    <!-- 新布局 -->
    <MudHidden Breakpoint="Breakpoint.MdAndUp">
        <_FooterMenus />
    </MudHidden>

    <_OffcanvasChatBox CObj="@CObj" />

</Main_Framework>




