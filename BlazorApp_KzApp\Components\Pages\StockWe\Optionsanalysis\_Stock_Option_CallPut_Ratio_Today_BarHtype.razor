﻿@code {
    [Parameter] public BlzHelperObj CObj { get; set; }
    [Parameter] public DateTime? DateStart { get; set; }
    [Parameter] public DateTime? DateEnd { get; set; }

    // 状态
    private int SelectedChartIndex = 0;

    // 数据源
    private List<eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas> datasLeft = new();
    private List<eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas> datasRight = new();
    private List<eZeroCore.Web.StkV2.Stock_Option_CallPut_Ratio> SeriesLeft = new();
    private List<eZeroCore.Web.StkV2.Stock_Option_CallPut_Ratio> SeriesRight = new();

    protected override async Task OnInitializedAsync()
    {
        await SwitchChart();
        sTitle = CObj.UserLang.GetLangValue("最大 Call Put 总权利金");
    }

    protected override async Task OnParametersSetAsync()
    {
        await SwitchChart(SelectedChartIndex);
    }

    async Task SwitchChart(int index = 0)
    {
        SelectedChartIndex = index;

        datasLeft.Clear();
        datasRight.Clear();

        #region...

        double AvgLeft = 0;
        double avgRight = 0;

        eZeroCore.Web.StkV2.Stock_Option_CallPut_RatioList stock_Option_Time_SeriesList = new();



        if (index == 0)
        {
            #region...
            SeriesLeft = stock_Option_Time_SeriesList.List("Call_Premium", DateStart: DateStart, DateEnd: DateEnd);
            if (SeriesLeft.Count > 0)
            {
                AvgLeft = SeriesLeft.Average(x => x.Call_Premium);
            }

            SeriesRight = stock_Option_Time_SeriesList.List("Put_Premium", DateStart: DateStart, DateEnd: DateEnd);
            if (SeriesRight.Count > 0)
            {
                avgRight = SeriesRight.Average(x => x.Put_Premium);
            }

            foreach (eZeroCore.Web.StkV2.Stock_Option_CallPut_Ratio stock in SeriesLeft)
            {
                datasLeft.Add(new eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas()
                    {
                        Title = "Call",
                        MainLabel = stock.Stock,
                    //MouseTipInfo = stock.CallPremium.ToString(),
                        LableA = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Call_Size),
                        LableL_ATitle = @CObj.UserLang.GetLangValue("总合约"),
                        LableB = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Call_Premium),
                        LableL_BTitle = @CObj.UserLang.GetLangValue("总权利金"),
                        WidthPerc = eZeroCore.Web.Stk.Chart.BarTypeAUI.GetBarWidth(stock.Call_Premium, AvgLeft, stock.RowNumber),
                        ShowLableTitleName = true,
                    });
            }
            foreach (eZeroCore.Web.StkV2.Stock_Option_CallPut_Ratio stock in SeriesRight)
            {
                datasRight.Add(new eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas()
                    {
                        Title = "Put",
                        MainLabel = stock.Stock,
                    //MouseTipInfo = stock.PutPremium.ToString(),
                        LableA = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Put_Size),
                        LableR_ATitle = @CObj.UserLang.GetLangValue("总合约"),
                        LableB = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Put_Premium),
                        LableR_BTitle = @CObj.UserLang.GetLangValue("总权利金"),
                        WidthPerc = eZeroCore.Web.Stk.Chart.BarTypeAUI.GetBarWidth(stock.Put_Premium, avgRight, stock.RowNumber),
                        ShowLableTitleName = true,
                    });
            }
            #endregion
        }
        else
        {
            #region...
            SeriesLeft = stock_Option_Time_SeriesList.List("TrendUpPremium", DateStart: DateStart, DateEnd: DateEnd);
            if (SeriesLeft.Count > 0)
            {
                AvgLeft = SeriesLeft.Average(x => x.TrendUpPremium);
            }

            //stock_Option_Time_SeriesList.OrderBy = "ORDER BY TradeDate DESC, TrendDownPremium desc";
            SeriesRight = stock_Option_Time_SeriesList.List("TrendDownPremium", DateStart: DateStart, DateEnd: DateEnd);
            if (SeriesRight.Count > 0)
            {
                avgRight = SeriesRight.Average(x => x.TrendDownPremium);
            }

            foreach (eZeroCore.Web.StkV2.Stock_Option_CallPut_Ratio stock in SeriesLeft)
            {
                datasLeft.Add(new eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas()
                    {
                        Title = @CObj.UserLang.GetLangValue("看涨"),
                        MainLabel = stock.Stock,
                    //MouseTipInfo = stock.TrendUpPremium.ToString(),
                        LableA = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Call_Size),
                        LableL_ATitle = @CObj.UserLang.GetLangValue("总合约"),
                        LableB = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.TrendUpPremium),
                        LableL_BTitle = @CObj.UserLang.GetLangValue("总权利金"),
                        WidthPerc = eZeroCore.Web.Stk.Chart.BarTypeAUI.GetBarWidth(stock.TrendUpPremium, AvgLeft, stock.RowNumber),
                        ShowLableTitleName = true,
                    });
            }
            foreach (eZeroCore.Web.StkV2.Stock_Option_CallPut_Ratio stock in SeriesRight)
            {
                datasRight.Add(new eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas()
                    {
                        Title = @CObj.UserLang.GetLangValue("看跌"),
                        MainLabel = stock.Stock,
                    //MouseTipInfo = stock.TrendDownPremium.ToString(),
                        LableA = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Put_Size),
                        LableR_ATitle = @CObj.UserLang.GetLangValue("总合约"),
                        LableB = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.TrendDownPremium),
                        LableR_BTitle = @CObj.UserLang.GetLangValue("总权利金"),
                        WidthPerc = eZeroCore.Web.Stk.Chart.BarTypeAUI.GetBarWidth(stock.TrendDownPremium, avgRight, stock.RowNumber),
                        ShowLableTitleName = true,
                    });
            }
            #endregion
        }


        #endregion

        //StateHasChanged();

        await Task.Delay(0);
    }

    //eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date.ToString("yyyy-MM-dd")

    string sTitle = "";

}


<_Bar_Template CObj=@CObj ID="@eZeroCore.EString.GetMd5HashID(sTitle)" ClassName="d-block">
    <TitleContent>
        <!-- PC端显示 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
            <div style="display:flex; align-items: center;">
                <span style="font-weight: 900; font-size: 14px;display: flex; align-items: center;color:rgba(245,156,26,1);">
                    @sTitle
                </span>
                <div style="margin-left: 8px;height: 20px; display: flex; align-items: center;background-color: rgba(245,156,26,1); color: #2d353c; padding: 4px 10px; border-radius: 8px; font-size: 12px; font-weight: bold;">
                    @if (DateStart.HasValue && DateEnd.HasValue && DateStart.Value.Date != DateEnd.Value.Date)
                    {
                        @($"{DateStart.Value:yyyy-MM-dd} ~ {DateEnd.Value:yyyy-MM-dd}")
                    }
                    else
                    {
                        @(DateStart?.ToString("yyyy-MM-dd") ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().ToString("yyyy-MM-dd"))
                    }
                </div>
            </div>
        </MudHidden>
        <!-- 移动端显示 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <div style="display:flex; align-items: center;justify-content">
                <span style="font-weight: 900; font-size: 14px;display: flex; align-items: center;color:rgba(245,156,26,1);">
                    @sTitle
                </span>
                <div style="margin-left: 8px;height: 20px; display: flex; align-items: center;background-color: rgba(245,156,26,1); color: #2d353c; padding: 4px 10px; border-radius: 8px; font-size: 12px; font-weight: bold;">
                    @if (DateStart.HasValue && DateEnd.HasValue && DateStart.Value.Date != DateEnd.Value.Date)
                    {
                        @($"{DateStart.Value:yyyy-MM-dd} ~ {DateEnd.Value:yyyy-MM-dd}")
                    }
                    else
                    {
                        @(DateStart?.ToString("yyyy-MM-dd") ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().ToString("yyyy-MM-dd"))
                    }
                </div>
            </div>
        </MudHidden>
    </TitleContent>

    <ChildContent>
        <!-- PC端显示 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
            <div class="btn-tab-group">
                <button @onclick="()=>SwitchChart(0)" type="button" class="btn btn-tab @(SelectedChartIndex==0?"active":"")">
                    Call VS Put
                </button>
                <button @onclick="()=>SwitchChart(1)" type="button" class="btn btn-tab @(SelectedChartIndex==1?"active":"")">
                    @CObj.UserLang.GetLangValue("看涨 VS 看跌")
                </button>
            </div>
        </MudHidden>
        <!-- 移动端显示 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <div class="btn-tab-group">
                <button @onclick="()=>SwitchChart(0)" type="button" class="btn btn-tab @(SelectedChartIndex==0?"active":"")">
                    Call VS Put
                </button>
                <button @onclick="()=>SwitchChart(1)" type="button" class="btn btn-tab @(SelectedChartIndex==1?"active":"")">
                    @CObj.UserLang.GetLangValue("看涨 VS 看跌")
                </button>
            </div>
        </MudHidden>
    </ChildContent>

    <HtypeBar>
        <div style="background: #000; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
            <!-- 添加水平滚动容器 -->
            <div style="width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch;">
                <div style="display: flex; min-width: 500px;">
                    <!-- 设置最小宽度确保内容在窄屏上也能完整显示 -->
                    <div class="col-6" style="border-right: 1px solid #333;">
                        <_BarHtype_Template CObj="@CObj" Datas="@datasLeft" EnableNavigation="true" />
                    </div>
                    <div class="col-6">
                        <_BarHtype_Template CObj="@CObj" Datas="@datasRight" Left=false EnableNavigation="true" />
                    </div>
                </div>
            </div>

            <!-- 移动端滑动提示 -->
            <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="false">
                <div class="d-flex justify-content-center py-1">
                    <small class="text-muted">@CObj.UserLang.GetLangValue("← 左右滑动查看更多 →")</small>
                </div>
            </MudHidden>
        </div>
    </HtypeBar>
</_Bar_Template>