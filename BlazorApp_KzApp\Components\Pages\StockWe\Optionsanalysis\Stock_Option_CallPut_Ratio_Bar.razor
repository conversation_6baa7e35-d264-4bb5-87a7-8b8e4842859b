@using System.Text.Json

@inject NavigationManager NavigationManager
@inject IJSRuntime JS

@implements IDisposable

@code {
    [Parameter] public BlzHelperObj? CObj { get; set; }
    [Parameter] public bool IsAdmin { get; set; }

    private int SelectedChartIndex = 0;
    private bool ShouldRenderEnabled = true;

    public DateTime SetDate { get; set; }
    public DateTime EndDate { get; set; } = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime();

    private readonly string _SearchStringDefault = "Total";
    private string _SearchString = "";

    private string SearchString
    {
        get => !string.IsNullOrEmpty(_SearchString) ? _SearchString : _SearchStringDefault;
        set => _SearchString = value;
    }

    protected override async Task OnInitializedAsync()
    {
        SetDate = DateTime.UtcNow.AddMonths(-6);
        await Task.Delay(0);
    }

    protected override bool ShouldRender() => ShouldRenderEnabled;

    public void Dispose()
    {
        // 清理资源
    }

    public void OnEnterPressed(string inputValue)
    {
        InvokeAsync(async () =>
        {
            SearchString = inputValue;
            await SwitchChart(SelectedChartIndex);
        });
    }

    public async Task Refresh()
    {
        await dbgLable.Refresh();
        await SwitchChart(SelectedChartIndex);
    }

}
@code
{
    string dateArea = "";
    string sSearchStock = "";
    string y1Json = "";
    string y2Json = "";
    string xJson = "";
    string lineJson = "";

    string DomId = "Option_CallPut_Ratio_BarChart";

    string sLabs = "";

    eZeroCore.Web.StkV2.Stock_Option_CallPut_RatioList DbList = new();
    List<eZeroCore.Web.StkV2.Stock_Option_CallPut_Ratio> lists = new();
    async Task OnInitAsync()
    {
        #region...

        DbList = new()
            {
                SetDate = SetDate,
                MainOrderBy = "CreateDate asc",
                Fields = "Put_Premium,Call_Premium,Price,CreateDate,TrendUpPremium,TrendDownPremium"
            };
        if (!string.IsNullOrEmpty(SearchString))
        {
            DbList.StockSearch = SearchString;
        }
        lists = DbList.List(Size: 200);

        if (SelectedChartIndex == 0)
        {
            sLabs = CObj?.UserLang.GetLangValue("Put总权利金,Call总权利金,代号走势（默认标普500）") ?? "Put总权利金,Call总权利金,代号走势（默认标普500）";
            y1Json = JsonSerializer.Serialize(lists.Select(item => item.Put_Premium).ToList());
            y2Json = JsonSerializer.Serialize(lists.Select(item => item.Call_Premium).ToList());
        }
        else
        {
            sLabs = CObj?.UserLang.GetLangValue("看跌总权利金,看涨总权利金,代号走势（默认标普500）") ?? "看跌总权利金,看涨总权利金,代号走势（默认标普500）";
            y1Json = JsonSerializer.Serialize(lists.Select(item => item.TrendDownPremium).ToList());
            y2Json = JsonSerializer.Serialize(lists.Select(item => item.TrendUpPremium).ToList());
        }
        xJson = JsonSerializer.Serialize(lists.Select(item => item.CreateDate.ToString("MM/dd")).ToList());
        lineJson = JsonSerializer.Serialize(lists.Select(item => item.Price).ToList());
        StateHasChanged();

        await Task.Delay(0);
        #endregion
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await OnInitAsync();
            if (!string.IsNullOrEmpty(y1Json))
            {
                await JS.InvokeVoidAsync($"StockOptionBar.init", DomId, y1Json, y2Json, xJson, lineJson, sLabs, CObj?.IsMobile == true ? 1 : 0);
            }
        }
    }
    async Task SwitchChart(int index = 0)
    {
        SelectedChartIndex = index;
        await OnInitAsync();
        await JS.InvokeVoidAsync($"StockOptionBar.initOption", y1Json, y2Json, xJson, lineJson, sLabs, true);
        if (eZeroCore.AppSettings.IsTestModeAll)
        {
            //await JS.InvokeVoidAsync("console.log", $"StockOptionBar.initOption:" + DateTime.Now.ToString());
        }
    }

     _DebugLable dbgLable;
}

<_Bar_Template CObj=@CObj ID="@DomId" Style="flex-direction:column">
    <TitleContent>
        <div style="display: flex; flex-direction: row; flex-wrap: nowrap; justify-content: space-between; align-items: center; width: 100%;">
            <span style="font-size: 14px; display: flex; align-items: center;color:rgba(245,156,26,1);font-weight:900">
                @(CObj?.UserLang.GetLangValue("历史 Call vs Put 总权利金") ?? "历史 Call vs Put 总权利金")
            </span>
            <div style="margin-left: 8px;height: 20px; display: flex; align-items: center;background-color: rgba(245,156,26,1); color: #2d353c; padding: 4px 10px; border-radius: 8px; font-size: 12px; font-weight: bold; white-space: nowrap;">
                @(SearchString.Equals(_SearchStringDefault) ? (CObj?.UserLang.GetLangValue("整个市场 ") ?? "整个市场 ") : SearchString + " ")@eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().ToString("yyyy-MM-dd")
            </div>
        </div>
    </TitleContent>
    <ChildContent>
        <div class="btn-tab-group mx-auto">
            <button @onclick="()=>SwitchChart(0)" type="button" class="btn btn-tab @(SelectedChartIndex==0?"active":"")">
                Call VS Put
            </button>
            <button @onclick="()=>SwitchChart(1)" type="button" class="btn btn-tab @(SelectedChartIndex==1?"active":"")">
                @(CObj?.UserLang.GetLangValue("看涨 VS 看跌") ?? "看涨 VS 看跌")
            </button>
        </div>
    </ChildContent>
</_Bar_Template>
