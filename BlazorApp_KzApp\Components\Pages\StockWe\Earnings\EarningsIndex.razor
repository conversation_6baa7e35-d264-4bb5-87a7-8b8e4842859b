﻿@using <PERSON>la<PERSON>App_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@using eZeroCore.Web.BlazorServerUI
@using System.Globalization
@using MudBlazor
@using Ra<PERSON>zen

@inject IJSRuntime JS
@inject NavigationManager NavigationManagerObj

@page "/Earnings/{param?}"

@code {
	[Parameter]
	public string param { get; set; } = "";

	[CascadingParameter]
	public BlzHelperObj? CObj { get; set; }
	eZeroCore.Users.User User { get; set; } = new();
	eZeroCore.Lang lang = new();

	// 添加共享的日期选择逻辑
	public DateTime? DateStart { get; set; } = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date;
	public DateTime? DateEnd { get; set; } = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date;
	DateRange DateRange
	{
		get => new(DateStart, DateEnd);
		set
		{
			if (value != null)
			{
				DateStart = value.Start;
				DateEnd = value.End;
				StateHasChanged(); // 通知UI更新
			}
		}
	}
	protected override async Task OnInitializedAsync()
	{
		User = new(CObj.UserId);
		lang = new(User);
		await Task.CompletedTask;
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await Task.CompletedTask;
	}

	string SearchString;
	async Task OnSearch(string value)
	{
		SearchString = value.ToUpper();
		if (AppSettings.IsTestModeAll)
		{
			await JS.InvokeVoidAsync("console.log", $"OnSearch:【{SearchString}】1");
		}
		await UpdateTableWithSearchString(SearchString);
	}

	// 引用表格组件
	Tb_StockEarningCalendar _stockBlockTable;

	// 添加更新表格方法
	async Task UpdateTableWithSearchString(string searchValue)
	{
		if (_stockBlockTable != null)
		{
			await _stockBlockTable.SearchByStockCode(searchValue);
		}
	}

	RzSearchInputBox inputBox;
	async Task SearchButtonClick()
	{
		await inputBox.HandleSearchKeyDown();
	}
}

<Main_Framework>

	<SubscriptionV3 PaymentVisable="@(!eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId)))">

		<!-- PC端搜索框 -->
		<MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
			<div class="p-2 text-end UIFrameworkHeader position-relative z-5">
				<div style="margin-top:35px; margin-bottom:-180px;margin-right:230px;">
					<RzSearchInputBox OnSearch="@OnSearch" Placeholder="@CObj.UserLang.GetLangValue("搜索代号")" />
				</div>
			</div>
		</MudHidden>

		<!-- 移动端搜索框 -->
		<MudHidden Breakpoint="Breakpoint.MdAndUp">
			<div class="p-2 UIFrameworkHeader position-relative z-5 z-center mt-1" style="display:flex; align-items:center; gap:8px;">
				<div>
					<RzSearchInputBox @ref=inputBox OnSearch="@OnSearch" Placeholder="@CObj.UserLang.GetLangValue("搜索代号")" />
				</div>
				<_SearchButton Text="@CObj.UserLang.GetLangValue("搜索")" OnClick="@SearchButtonClick" MarginLeft="0" MarginTop="0" />
			</div>
		</MudHidden>
		
		<!-- 日期选择器 -->
		<div class="d-flex justify-content-end position-relative z-5">
			<div style="width:200px;margin-top:20px; margin-bottom:-180px;height:40px;" class="me-2">
				<MudDateRangePicker Culture="@CultureInfo.GetCultureInfo("zh-Hans")"
									PickerVariant="PickerVariant.Dialog"
									ShowToolbar="false"
									TitleDateFormat="yyyy,MM,dd"
									FirstDayOfWeek="DayOfWeek.Sunday"
									Clearable="false"
									DateFormat="MM/dd/yy"
									@bind-DateRange="DateRange" />
			</div>
		</div>

		<!-- 表格 -->
		<Tb_StockEarningCalendar @ref="_stockBlockTable" CObj="@CObj" DateStart="@DateStart" DateEnd="@DateEnd" />

		<!-- 新布局 -->
		<MudHidden Breakpoint="Breakpoint.MdAndUp">
			<_FooterMenus />
		</MudHidden>

		<_OffcanvasChatBox CObj="@CObj" />

	</SubscriptionV3>
</Main_Framework>
