@inject IJSRuntime JS
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web.Virtualization
@inject NavigationManager NavigationManagerObj

@code {

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public eZeroCore.Web.Column ParentColumn { get; set; } = new();

    [Parameter]
    public string ParentDomId { get; set; } = "";

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public bool IsRoot { get; set; }
    [Parameter]
    public string Icon { get; set; } = "";
    [Parameter]
    public string MatchUrl { get; set; } = "";   

    [Parameter]
    public string className { get; set; } = "";

    [Parameter]
    public bool OnClickUseEvent { get; set; }

    bool Expended { get; set; }
    bool Active { get; set; }
    string ParentColumnId { get; set; } = "";

    string sIdTree = "";

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(ParentColumn.ColumnIdTree))
        {
            ParentColumnId = ParentColumn.ColumnId;
            DomId = $"{ParentColumnId}_{Math.Abs(ParentColumn.ColumnIdTree.GetHashCode())}";
            MatchUrl = ParentColumn.GetColumnUrl();
            //sIdTree = CObj.TransferColumnIdTree ?? "";
            if (CObj.ThisColumn is not null)
            {
                sIdTree = CObj.ThisColumn.ColumnIdTree;
            }
            if (string.IsNullOrEmpty(ParentDomId))
            {
                ParentDomId = NavbarVerticalMenus_Framework.DomID;
                IsRoot = true;
            }
        }
        else
        {
            DomId = $"MenuItem_{Math.Abs(DateTime.Now.GetHashCode())}";
        }
        //bool hasSubColumn = ParentColumn.ExistSubColumn;
        LinkDomId = "link" + DomId;
        bool hiddenVisible = CObj.UserIsAdmin || (CObj.ThisChannel is not null && CObj.ThisChannel.CreateMemberId.Equals(CObj.UserId, StringComparison.OrdinalIgnoreCase));
        /*
        if(hiddenVisible)
        {
            hasSubColumn = eZeroCore.Web.Column.GetColumnCount(ParentColumn.ColumnId) > 0;
            }
        */
        /*
        if (hasSubColumn)
        {
            List<eZeroCore.Web.Column> lists = await (new eZeroCore.Web.ColumnList()
            {
                ParentId = ParentColumnId,
                HiddenVisible = true
                }.ListAsync(eZeroCore.Db.CacheHelperV2.CacheNameType.Col_MenusTree));
            ParentColumn.ExistSubColumn = lists.Count(column => column.SettingsEntity.ContentForSettings == false) > 0;
            }
        */

    if (string.IsNullOrEmpty(Icon))
        {
            if (IsRoot)
            {
                var icon = string.IsNullOrWhiteSpace(ParentColumn.SettingsEntity.Icon) ? "" : ParentColumn.SettingsEntity.Icon;
                Icon = icon;
            }
            else
            {
                Icon = ParentColumn.SettingsEntity.Icon;
            }
        }

        if (!Active && !string.IsNullOrWhiteSpace(MatchUrl))
        {
            if (MatchUrl.Equals("/"))
            {
                Active = CObj.ThisUrl_NotLoginOrRegister.Equals(MatchUrl, StringComparison.OrdinalIgnoreCase);
            }
            else
            {
                Active = CObj.ThisUrl_NotLoginOrRegister.Contains(MatchUrl, StringComparison.OrdinalIgnoreCase);
            }
            if (!string.IsNullOrEmpty(CObj.ThisDoc?.ColumnId ?? ""))
            {
                Active = (CObj.ThisDoc?.ColumnId ?? "").Equals(ParentColumnId, StringComparison.OrdinalIgnoreCase);
            }
        }
        Expended = sIdTree.Contains(ParentColumnId, StringComparison.OrdinalIgnoreCase);

        Icon += " ml-1";

        await Task.Delay(0);
    }

    string DomId = "";
    string LinkDomId = "";
    string FocalPointdClassName = "text-warning";

    string GetColumnName(string name)
    {
        if (name.StartsWith("col", StringComparison.OrdinalIgnoreCase) && !name.StartsWith("column", StringComparison.OrdinalIgnoreCase))
        {
            return eZeroCore.Web.Column.GetColumnName(name) ?? name;
        }
        return name;
    }

    string sStyleValue
    {
        get
        {
            return $"color:var({(eZeroCore.Web.UI.Bs5V2.GetSafeColorClassName(ParentColumn.SettingsEntity.SafecolorClass))})";
        }
    }

    bool CheckIsLocked()
    {
        int userLv = CObj.UserOrgLv;
        if (CObj.IsStk)
        {
            return ParentColumn.SettingsEntity.MinUserLv > userLv;
        }
        else
        {
            return false;
        }
    }

    async Task OnClickColumn(string columnid, string url = "")
    {
        if (OnClickUseEvent)
        {
            if (!string.IsNullOrWhiteSpace(url))
            {
                NavigationManagerObj.NavigateTo(url);
            }
            //await MenusEventService.PublishAsync(columnid);
            await Generic.Js(JS, "eSc.NavbarEventSelected", LinkDomId);
            await Task.Delay(500);
            Active = true;
            //StateHasChanged();
        }
    }
}

<__NavItem_wrapper UseWrapper="IsRoot" DomID="@DomId">

    @if (!ParentColumn.ExistSubColumn)
    {
        <a id="@LinkDomId" class="nav-link @(IsRoot?"label-1":"") @(Active?"active":"") @(ParentColumn.Hidden?"opacity-25":"") @className" href="@(Active || OnClickUseEvent?"javascript:;":MatchUrl)" data-cid="@ParentColumn.ColumnId.ToUpper()" @onclick="async ()=>{ await OnClickColumn(ParentColumn.ColumnId,MatchUrl);}">
            @*w-75 w-md-100*@
            <div class="d-flex align-items-center w-75 w-md-100 dot @(nameof(_NavItem))" data-url="@CObj.ThisUrl_NotLoginOrRegister" style="@sStyleValue" data-colurl="@MatchUrl">
                @{
                    bool hasIcon = CheckIsLocked() || !string.IsNullOrWhiteSpace(Icon);
                }
                @if (IsRoot)
                {
                    @* ICON *@
                    @if (CheckIsLocked())
                    {
                        <span class="nav-link-icon">
                            <i class="fa-sharp text-info-light fa-solid fa-lock-keyhole fa-fw ml-1"></i>
                        </span>
                    }
                    else if (!string.IsNullOrWhiteSpace(Icon))
                    {
                        <span class="nav-link-icon">
                            <i class="@Icon fa-fw @(ParentColumn.SettingsEntity.Bold?FocalPointdClassName:"")"></i>
                        </span>
                    }
                    <span class="nav-link-text-wrapper">
                        <span class="nav-link-text @(ParentColumn.SettingsEntity.Bold?FocalPointdClassName:"")">
                            @GetColumnName(ParentColumn.ColumnName).Truncate(Main_Framework.Settings.LeftOrTopSubMenusMaxString, "...")
                            @ChildContent
                        </span>
                    </span>
                }
                else
                {
                    <span class="nav-link-text" style="@sStyleValue">
                        @if (!string.IsNullOrEmpty(Icon))
                        {
                            @* ICON *@
                            <i class="@Icon fa-fw"></i>
                        }
                        @ParentColumn.ColumnName.Truncate(Main_Framework.Settings.LeftOrTopSubMenusMaxString, "...")
                    </span>
                }
            </div>
        </a>
    }
    else
    {
        <a id="@LinkDomId" class="nav-link dropdown-indicator @(IsRoot?"label-1":"") @(Active?"active":"") @(ParentColumn.Hidden?"opacity-25":"")  @className" href="#@DomId" @onclick="async ()=>{ await OnClickColumn(ParentColumn.ColumnId,MatchUrl);}" role="button" data-bs-toggle="collapse" data-cid="@ParentColumn.ColumnId.ToUpper()"
        aria-expanded="@(Expended.ToString().ToLower())" aria-controls="@DomId">
            <div class="d-flex align-items-center function" style="@sStyleValue" data-colurl="@MatchUrl">
                <div class="dropdown-indicator-icon-wrapper">
                    <span class="fas fa-caret-right dropdown-indicator-icon"></span>
                </div>
                @if (IsRoot && !string.IsNullOrWhiteSpace(Icon))
                {
                    <span class="nav-link-icon">
                        @* ICON *@
                        <i class="@Icon fa-fw"></i>
                    </span>
                }
                <span class="nav-link-text @(string.IsNullOrWhiteSpace(Icon) && IsRoot ?"ms-2":"")">
                    @ParentColumn.ColumnName.Truncate(Main_Framework.Settings.LeftOrTopSubMenusMaxString, "...")
                </span>
                @* NewIcon:
            <span class="fa-solid fa-circle text-info ms-1 new-page-indicator" style="font-size:5px;"></span>
            *@
            </div>
        </a>
        <div class="parent-wrapper @(IsRoot?"label-1":"")">
            <ul class="nav collapse parent placeholder-glow @(Expended?"show":"")" data-bs-parent="#@ParentDomId" id="@DomId">
                @if (IsRoot)
                {
                    <li class="collapsed-nav-item-title d-none">
                        @ParentColumn.ColumnName.Truncate(Main_Framework.Settings.LeftOrTopSubMenusMaxString, "...")
                    </li>
                }
                <Virtualize ItemsProvider="new MudUI.MenusVirtualize(CObj, ParentColumnId).LoadColumnsAsync" Context="column" ItemSize="6">
                    <ItemContent>
                        @if(!column.Hidden || (column.Hidden && LeftMenus_NavItemGroup.CheckHiddenColAccess(CObj,column.SettingsEntity.HiddenColumnAccess)))
                        {
                            <li class="nav-item">
                                <_NavItem OnClickUseEvent="@OnClickUseEvent" ParentColumn="@column" ParentDomId="@DomId" @key="column.ColumnId.GetHashCode()" />
                            </li>
                        }
                    </ItemContent>
                    <Placeholder>
                        Loading...
                    </Placeholder>
                </Virtualize>
            </ul>
        </div>
    }
</__NavItem_wrapper>

@*/lib/Ph117/public/assets/*@
@*
     <div class="nav-item-wrapper">
        <a class="nav-link label-1" href="/lib/Ph117/public/index.html"
           role="button" data-bs-toggle="" aria-expanded="false">
            <div class="d-flex align-items-center">
                <span class="nav-link-icon">
                    <i class="@ColObj.SettingsEntity.Icon fa-fw"></i>
                </span>
                <span class="nav-link-text-wrapper">
                    <span class="nav-link-text">
                        @ColObj.ColumnName
                    </span>
                </span>
            </div>
        </a>
    </div>

    <a class="nav-link dropdown-indicator" href="#@domId" data-bs-toggle="collapse" aria-expanded="false" aria-controls="@domId">
                            <div class="d-flex align-items-center">
                                <div class="dropdown-indicator-icon-wrapper">
                                    <span class="fas fa-caret-right dropdown-indicator-icon"></span>
                                </div>
                                <span class="nav-link-text">Customer</span>
                                <span class="badge ms-2 badge badge-phoenix badge-phoenix-warning">New</span>
                            </div>
                        </a>
                        <!-- more inner pages -->
                        <div class="parent-wrapper">
                            <ul class="nav collapse parent" data-bs-parent="#@DomId" id="@domId">
                                <li class="nav-item">
                                    <a class="nav-link" href="hotel/customer/homepage.html">
                                        <div class="d-flex align-items-center">
                                            <span class="nav-link-text">Homepage</span>
                                        </div>
                                    </a>
                                    <!-- more inner pages -->
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="hotel/customer/hotel-details.html">
                                        <div class="d-flex align-items-center">
                                            <span class="nav-link-text">Hotel details</span>
                                        </div>
                                    </a>
                                    <!-- more inner pages -->
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="hotel/customer/hotel-compare.html">
                                        <div class="d-flex align-items-center">
                                            <span class="nav-link-text">Hotel compare</span>
                                        </div>
                                    </a>
                                    <!-- more inner pages -->
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="hotel/customer/checkout.html">
                                        <div class="d-flex align-items-center">
                                            <span class="nav-link-text">Checkout</span>
                                        </div>
                                    </a>
                                    <!-- more inner pages -->
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="hotel/customer/payment.html">
                                        <div class="d-flex align-items-center">
                                            <span class="nav-link-text">Payment</span>
                                        </div>
                                    </a>
                                    <!-- more inner pages -->
                                </li>
                            </ul>
                        </div>
 *@