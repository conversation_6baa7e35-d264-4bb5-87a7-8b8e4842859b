﻿@inject IJSRuntime JS
@code {
    protected override bool ShouldRender()
    {
        return false;
    }
    
    [Parameter]
    public BlzHelperObj CObj { get; set; } 
    [Parameter]
    public bool IsAdmin { get; set; }

    public string DomID { get; set; } = eZeroCore.EString.CreateId("DLabel");

    protected override void OnInitialized()
    {
        if (eZeroCore.AppSettings.IsTestModeAll)
        {
            IsAdmin = true;
        }
        base.OnInitialized();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Refresh();
        }
    }

    public async Task Refresh()
    {
        try
        {
            await JS.InvokeVoidAsync("StkDebugAdminData", DomID);
        }
        catch { }
        //await JS.InvokeVoidAsync("console.log", $"{DomID}:Refresh:" + DateTime.Now.ToString());
    }
}

@if (!CObj.IsMobile && IsAdmin)
{
    <label class="DebugLabel mx-2">
        AdminOnly:
        <small id="@DomID"></small>
    </label>
}