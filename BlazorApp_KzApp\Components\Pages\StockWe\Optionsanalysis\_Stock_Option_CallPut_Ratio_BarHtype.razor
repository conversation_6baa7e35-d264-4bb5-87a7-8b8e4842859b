﻿@code {
    [Parameter] public BlzHelperObj CObj { get; set; }
    [Parameter] public bool IsAdmin { get; set; }
    [Parameter] public DateTime? DateStart { get; set; }
    [Parameter] public DateTime? DateEnd { get; set; }

    // 数据源
    private eZeroCore.Web.Stk.StockOptionSQL stockOptionSQLCall;
    private List<eZeroCore.Web.Stk.StockOptionSQL> stocksCall = new();
    private eZeroCore.Web.Stk.StockOptionSQL stockOptionSQLPut;
    private List<eZeroCore.Web.Stk.StockOptionSQL> stocksPut = new();

    // 图表数据
    private List<eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas> datasLeft = new();
    private List<eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas> datasRight = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        sTitle = CObj.UserLang.GetLangValue("期权总权利金激增");
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        // 使用传入的日期，如果未指定则使用默认日期
        DateTime date = DateStart ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date;

        stockOptionSQLCall = new("call", date);
        stockOptionSQLPut = new("put", date);

        stocksCall = await stockOptionSQLCall.GetStockCallPutList(DateStart, DateEnd);
        stocksPut = await stockOptionSQLPut.GetStockCallPutList(DateStart, DateEnd);
        
        datasLeft.Clear();
        datasRight.Clear();
        
        foreach (eZeroCore.Web.Stk.StockOptionSQL stock in stocksCall)
        {
            datasLeft.Add(new eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas()
                {
                    Title = @CObj.UserLang.GetLangValue("Call总权利金激增"),
                    MainLabel = stock.Stock,
                //MouseTipInfo = stock.RowNumber.ToString(),
                    LableA = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Total_Size),
                    LableL_ATitle = @CObj.UserLang.GetLangValue("总合约"),
                    LableB = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Total_Premium),
                    LableL_BTitle = @CObj.UserLang.GetLangValue("总权利金"),
                    WidthPerc = eZeroCore.Web.Stk.Chart.BarTypeAUI.GetBarWidth(stock.Total_Premium, stockOptionSQLCall.AverageTotalPremium, stock.RowNumber),
                    ShowLableTitleName = true,

                });
        }
        foreach (eZeroCore.Web.Stk.StockOptionSQL stock in stocksPut)
        {
            datasRight.Add(new eZeroCore.Web.Stk.Chart.BarTypeAUI.Datas()
                {
                    Title = @CObj.UserLang.GetLangValue("Put总权利金激增"),
                    MainLabel = stock.Stock,
                //MouseTipInfo = stock.RowNumber.ToString(),
                    LableA = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Total_Size),
                    LableR_ATitle = @CObj.UserLang.GetLangValue("总合约"),
                    LableB = eZeroCore.ENumeric.ConvertNumberToMorKString(stock.Total_Premium),
                    LableR_BTitle = @CObj.UserLang.GetLangValue("总权利金"),
                    WidthPerc = eZeroCore.Web.Stk.Chart.BarTypeAUI.GetBarWidth(stock.Total_Premium, stockOptionSQLPut.AverageTotalPremium, stock.RowNumber),
                    ShowLableTitleName = true,
                });
        }

    }

    string sAddClassName = "";
    string sTitle = "";
    //eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date.ToString("yyyy-MM-dd")
}

<_Bar_Template CObj=@CObj ID="@eZeroCore.EString.GetMd5HashID(sTitle)" ClassName="d-block">
    <TitleContent>
        <div style="display:flex; align-items: center;">
            <span style="font-weight: 900; font-size: 14px;display: flex; align-items: center;color:rgba(245,156,26,1);">
                @sTitle
            </span>
            <div style="margin-left: 8px;height: 20px; display: flex; align-items: center;background-color: rgba(245,156,26,1); color: #2d353c; padding: 4px 10px; border-radius: 8px; font-size: 12px; font-weight: bold;">
                @if (DateStart.HasValue && DateEnd.HasValue && DateStart.Value.Date != DateEnd.Value.Date)
                {
                    @($"{DateStart.Value:yyyy-MM-dd} ~ {DateEnd.Value:yyyy-MM-dd}")
                }
                else
                {
                    @(DateStart?.ToString("yyyy-MM-dd") ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().ToString("yyyy-MM-dd"))
                }
            </div>
        </div>
    </TitleContent>
    <HtypeBar>
        <div style="background: #000; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.2); margin-bottom: 15px !important;">
            <!-- 添加水平滚动容器 -->
            <div style="width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch;">
                <div style="display: flex; min-width: 500px;"> <!-- 设置最小宽度确保内容在窄屏上也能完整显示 -->
                    <div class="col-6" style="border-right: 1px solid #333;">
                        <_BarHtype_Template CObj="@CObj" Datas="@datasLeft" EnableNavigation="true" />
                    </div>
                    <div class="col-6">
                        <_BarHtype_Template CObj="@CObj" Datas="@datasRight" Left=false EnableNavigation="true" />
                    </div>
                </div>
            </div>
            
            <!-- 移动端滑动提示 -->
            <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="false">
                <div class="d-flex justify-content-center py-1">
                    <small class="text-muted">@CObj.UserLang.GetLangValue("← 左右滑动查看更多 →")</small>
                </div>
            </MudHidden>
        </div>
    </HtypeBar>
</_Bar_Template>

