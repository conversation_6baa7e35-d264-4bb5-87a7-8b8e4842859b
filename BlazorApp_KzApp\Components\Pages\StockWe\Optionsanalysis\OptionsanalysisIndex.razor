@using <PERSON><PERSON><PERSON>App_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@using eZeroCore.Web.BlazorServerUI
@using Ra<PERSON><PERSON>
@using System.Globalization

@inject IJSRuntime JS
@inject NavigationManager NavigationManagerObj

@page "/Optionsanalysis/{param?}"

@code {
    [Parameter] public string param { get; set; } = "";
    [CascadingParameter] public BlzHelperObj? CObj { get; set; }

    private bool isAdmin { get; set; }
    private bool displayDatabase { get; set; } = true;
    private bool displayChart { get; set; } = true;
    private bool ShouldRenderEnabled = true;

    private eZeroCore.Users.User User { get; set; } = new();
    private eZeroCore.Lang lang = new();

    // 日期选择
    public DateTime? DateStart { get; set; } = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date;
    public DateTime? DateEnd { get; set; } = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date;

    DateRange DateRange
    {
        get => new(DateStart, DateEnd);
        set 
        {
            if (value != null)
            {
                DateStart = value.Start;
                DateEnd = value.End;
                StateHasChanged(); 
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        if (CObj != null)
        {
            User = new(CObj.UserId);
            isAdmin = User.CheckIsAdministrator();
            lang = new(User);
            await Task.Delay(0);
            if (CObj.IsMobile)
            {
                //displayChart = false;
            }
        }
        else
        {
            User = new();
            isAdmin = false;
            lang = new();
            await Task.Delay(0);
        }
    }

    private Stock_Option_CallPut_Ratio_Bar _chartBar;
    private Tb_Today_Stock_Option_CallPut_Ratio _tableComponent;
    private RzSearchInputBox inputBox;

    private string SearchString;

    protected override bool ShouldRender() => ShouldRenderEnabled;

    private async Task OnSearch(string value)
    {
        SearchString = value.ToUpper();

        if (AppSettings.IsTestModeAll)
        {
            await JS.InvokeVoidAsync("console.log", $"OnSearch:【{SearchString}】");
        }

        _chartBar.OnEnterPressed(SearchString);
        await UpdateTableWithSearchString(SearchString);
    }

    private async Task UpdateTableWithSearchString(string searchValue)
    {
        if (_tableComponent != null)
        {
            await _tableComponent.SearchByStockCode(searchValue);
        }
    }

    private async Task SearchButtonClick()
    {
        await inputBox.HandleSearchKeyDown();
    }
}

<Main_Framework>
    <SubscriptionV3 PaymentVisable="@(!eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj?.UserId ?? "")))">

        <!-- PC端搜索框 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
            <div class="p-2 text-end UIFrameworkHeader position-relative z-5">
                <div style="margin-top:30px; margin-bottom:-180px; margin-right:267px;">
                    <RzSearchInputBox OnSearch="@OnSearch"
                                      Placeholder="@(CObj?.UserLang.GetLangValue("搜索代号") ?? "搜索代号")" />
                </div>
            </div>
        </MudHidden>

        <!-- 移动端搜索框 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <div class="p-2 UIFrameworkHeader position-relative z-5 z-center mt-1"
                 style="display:flex; align-items:center; gap:8px;">
                <div>
                    <RzSearchInputBox @ref="inputBox"
                                      OnSearch="@OnSearch"
                                      Placeholder="@(CObj?.UserLang.GetLangValue("搜索代号") ?? "搜索代号")" />
                </div>
                <_SearchButton Text="@(CObj?.UserLang.GetLangValue("搜索") ?? "搜索")"
                               OnClick="@SearchButtonClick"
                               MarginLeft="0"
                               MarginTop="0" />
            </div>
        </MudHidden>

        @if (CObj != null)
        {
            <Stock_Option_CallPut_Ratio_Bar @ref="_chartBar" CObj="@CObj" IsAdmin="@isAdmin" />
        }

        <!-- 日期选择器 -->
        <div class="d-flex justify-content-end position-relative z-5 me-3">
            <div style="width:200px; margin-bottom:-180px; margin-top:20px; height:40px;">
                <MudDateRangePicker Culture="@CultureInfo.GetCultureInfo("zh-Hans")"
                                    PickerVariant="PickerVariant.Dialog"
                                    ShowToolbar="false"
                                    TitleDateFormat="yyyy,MM,dd"
                                    FirstDayOfWeek="DayOfWeek.Sunday"
                                    Clearable="false"
                                    DateFormat="MM/dd/yy"
                                    @bind-DateRange="DateRange" />
            </div>
        </div>

        @if (CObj != null)
        {
            <!-- 数据表格 -->
            <Tb_Today_Stock_Option_CallPut_Ratio @ref="_tableComponent"
                                                  CObj="@CObj"
                                                  DateStart="@DateStart"
                                                  DateEnd="@DateEnd" />

            <!-- 图表组件 -->
            <div style="height:1000px; background:#000; margin-bottom:30px;">
                <_Stock_Option_CallPut_Ratio_BarHtype CObj="@CObj"
                                                      IsAdmin="@isAdmin"
                                                      DateStart="@DateStart"
                                                      DateEnd="@DateEnd" />
            </div>

            <div style="height:1150px; background:#000;">
                <_Stock_Option_CallPut_Ratio_Today_BarHtype CObj="@CObj"
                                                            DateStart="@DateStart"
                                                            DateEnd="@DateEnd" />
            </div>

            <_OffcanvasChatBox CObj="@CObj" />
        }

        <!-- 移动端底部菜单 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <_FooterMenus />
        </MudHidden>

    </SubscriptionV3>
</Main_Framework>
