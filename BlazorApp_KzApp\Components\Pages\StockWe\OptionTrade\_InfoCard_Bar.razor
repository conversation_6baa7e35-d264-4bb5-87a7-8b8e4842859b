﻿@using eZeroCore.Web.Stk.Data
@using System.Globalization
@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    bool liveModeSet = false;
    bool _liveDataUpload = true;
    bool LiveDataUpload
    {
        get => _liveDataUpload;
        set
        {
            if (_liveDataUpload != value)
            {
                _ = LiveModeChange(value);
            }
        }
    }
    async Task LiveModeChange(bool value)
    {
        if (!liveModeSet)
        {
            liveModeSet = true;
            _liveDataUpload = value;
            await Live.InvokeAsync(value);
            await Task.Delay(800);
            liveModeSet = false;
        }
    }

    [Parameter]
    public EventCallback<bool> Live { get; set; }

    private DateRange? _dateRangeValue;
    public DateRange? DateRangeValue
    {
        get => _dateRangeValue;
        set
        {
            if (value == null)
            {
                _dateRangeValue = null;
                SetDateRange.InvokeAsync(null);
                return;
            }
            _ = UpdateDateRange(value);
        }
    }

    [Parameter]
    public EventCallback<DateRange?> SetDateRange { get; set; }

    private DateTime startDate = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime();
    private DateTime endDate = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime();


    public string SearchString { get; set; } = "";

    protected override async Task OnInitializedAsync()
    {
        SetInitialDateRange();
        await Task.CompletedTask;
    }

    private bool _hasInit = false;
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !_hasInit)
        {
            _hasInit = true;
            await Init();
        }
    }

    private DateTime lastMarket = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime();
    private Stock_Option_CallPut_Ratio stock = new();

    /// <summary>
    /// live must is false
    /// </summary>
    /// <param name="live"></param>
    /// <param name="onlyChangeLiveMode"></param>
    /// <returns></returns>
    public async Task Init(bool? live = null, bool onlyChangeLiveMode = false, string searchString="")
    {
        SearchString = searchString;
        if (live is not null && !live.Value)
        {
            LiveDataUpload = false;
        }
        if (onlyChangeLiveMode) return;

        UpdateEndDate();
        string sWhere = GenerateWhereClause();
        await LoadStockData(sWhere);

        //await UpdateNumber();

        if (!lastMarket.Date.Equals(endDate.Date))
        {
            LiveDataUpload = false;
        }

        if (LiveDataUpload && timestamp is not null)
        {
            await timestamp.TimerTaskEvent();
        }
    }

    private void SetInitialDateRange()
    {
        if (DateRangeValue != null)
        {
            startDate = DateRangeValue?.Start?.Date ?? startDate;
            endDate = DateRangeValue?.End?.Date ?? endDate;
        }
        else
        {
            DateRangeValue = new DateRange
                {
                    Start = startDate,
                    End = startDate.Equals(endDate) ? null : endDate
                };
        }
    }

    private async Task UpdateDateRange(DateRange? value)
    {
        if (value?.Start.HasValue == true && value.End.HasValue)
        {
            var daysDifference = (value.End.Value - value.Start.Value).TotalDays;
            _dateRangeValue = daysDifference > 98
                ? new DateRange(value.Start, value.Start.Value.AddDays(98))
                : value;
        }
        else
        {
            _dateRangeValue = null;
        }
        await SetDateRange.InvokeAsync(_dateRangeValue);
        await Init();
    }

    private void UpdateEndDate()
    {
        endDate = DateRangeValue?.End ?? lastMarket;
    }

    string GenerateWhereClause()
    {
        string stockValue = string.IsNullOrEmpty(SearchString) ? "total" : SearchString;
        return $"CreateDate >= '{endDate}' and stock = '{stockValue}'";
    }

    //int Key1 = 0;
    //int Key2 = 0;
    private async Task LoadStockData(string sWhere)
    {
        Stock_Option_CallPut_Ratio.DataList stockList = new()
            {
                PageSize = 1,
                UseCache=false,
                SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending,
                AddWhere = sWhere
            };
        List<Stock_Option_CallPut_Ratio> stocks = await stockList.ListAsync();
        stock = stocks.FirstOrDefault() ?? new();
        
        /*
        Key1 = (stock.TrendUpPremium + stock.TrendDownPremium).GetHashCode();
        Key2 = (stock.Call_Premium + stock.Put_Premium).GetHashCode();
            if (Key1 == 0)
            {
            Key1 = eZeroCore.EString.GetNumberIdMix18(5).GetHashCode();    
            Key2 = eZeroCore.EString.GetNumberIdMix18(6).GetHashCode();
        }
        */
        
        if (_HalfPieA is not null)
        {
            await _HalfPieA.DrawChart(stock.TrendUpPremium, stock.TrendDownPremium, SearchString);
        }
        if (_HalfPieB is not null)
        {
            await _HalfPieB.DrawChart(stock.Call_Premium, stock.Put_Premium, SearchString);
        }
        /*
        long test = stock.TrendUpPremium;
        test.ToString();
    */
    }
    __HalfPie _HalfPieA, _HalfPieB;

    /*
    private string slideUpClass = "";
    private async Task UpdateNumber()
    {
        slideUpClass = "slide-up";
        StateHasChanged();
        await Task.Delay(1000);
        slideUpClass = "";
        StateHasChanged();
    }
    */

    _Timestamp timestamp;
    private bool CheckIsDateDisabled(DateTime date)
    {
        return (date - DateTime.Now.Date).Days > 0;
    }

    //private string sBadgeClass = "badge badge-phoenix badge-phoenix-secondary bg-body overflow-hidden";
    /*
     * <MudTooltip Color="Color.Dark" Text="注：操作行为会暂停实时更新" Arrow="true" Placement="Placement.Top">
                    <i class="fa-solid fa-circle-exclamation text-warning ms-1"></i>
     </MudTooltip>
    */
}

<MudThemeProvider IsDarkMode="true" />

<div class="row g-3 mx-xl-n3 mb-2 @SearchString">

    <__HalfPie @ref=_HalfPieA />
    <__HalfPie @ref=_HalfPieB GreenTitleTip="Call" RedTitleTip="Put" GreenTitle="Call" RedTitle="Put" />

    <div class="col-12 col-xl-3">

        <_SmallBoxFramework ClassName="col mb-2" Icon="fas fa-stopwatch" TitleClass="mb-1" ContentClass="d-flex align-items-center">
            <Title>
                @CObj.UserLang.GetLangValue("实时更新"):
                <_Timestamp Inline="true" ClassName="fs-9" @ref=timestamp />
            </Title>
            <ChildContent>
                <MudSwitch Class="mx-auto" @bind-Value="@LiveDataUpload" Label="@CObj.UserLang.GetLangValue("实时更新")" Color="Color.Success" />
            </ChildContent>
        </_SmallBoxFramework>

        <_SmallBoxFramework ClassName="col" Icon="fas fa-calendar-alt" TitleClass="mb-0" ContentClass="d-flex align-items-center mb-2">
            <Title>
                @CObj.UserLang.GetLangValue("交易日")：@stock.CreateDate.ToString("MM月dd日 dddd", new CultureInfo("zh-cn"))
            </Title>
            <ChildContent>
                <MudDateRangePicker Culture="@CultureInfo.GetCultureInfo("zh-Hans")"
                                    PickerVariant="PickerVariant.Dialog"
                                    ShowToolbar="false"
                                    TitleDateFormat="yyyy,MM,dd"
                                    FirstDayOfWeek="DayOfWeek.Sunday"
                                    Clearable="true"
                                    DateFormat="MM/dd/yy"
                                    @bind-DateRange="DateRangeValue" />
            </ChildContent>
        </_SmallBoxFramework>

    </div>


</div>
